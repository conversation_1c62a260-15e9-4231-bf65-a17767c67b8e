# Auth0 Actions for Kaleido Talent

This directory contains Auth0 Actions that need to be deployed to your Auth0 tenant.

## Current Actions

### 1. Post Login - Assign User Role
- **File**: `post-login-assign-role.js`
- **Trigger**: Post Login
- **Purpose**: Assigns primary user role to JWT tokens

## Deployment Instructions

### Manual Deployment (Recommended for now)

1. Login to your Auth0 Dashboard
2. Navigate to **Actions** → **Flows** → **Login**
3. Click **Custom** tab and create a new Action
4. Name it: "Assign User Role"
5. Copy the contents of `post-login-assign-role.js`
6. Deploy the action
7. Add it to the Login flow

### Automated Deployment (Future)

You can use the Auth0 Deploy CLI for automated deployment:

```bash
# Install Auth0 Deploy CLI
npm install -g auth0-deploy-cli

# Create config file
cat > auth0-config.json << EOF
{
  "AUTH0_DOMAIN": "headstart.uk.auth0.com",
  "AUTH0_CLIENT_ID": "${AUTH0_CLIENT_ID}",
  "AUTH0_CLIENT_SECRET": "${AUTH0_CLIENT_SECRET}",
  "AUTH0_ALLOW_DELETE": false
}
EOF

# Deploy actions
a0deploy import --config_file auth0-config.json --input_file auth0-actions/
```

## Dual-Role System Integration

The current Auth0 Action handles **primary roles only**. The dual-role system works as follows:

1. **Primary Role** (stored in Auth0):
   - Set during signup/first login
   - Included in JWT tokens
   - Cannot be changed without admin intervention

2. **Additional Roles** (stored in app database):
   - Managed by the application
   - Checked server-side via `RolesService.hasRole()`
   - Can be added/removed dynamically

### Why Not Store Additional Roles in JWT?

1. **JWT Size**: Adding arrays of roles increases token size
2. **Synchronization**: Would require webhook to Auth0 on every role change
3. **Flexibility**: Database checks allow immediate role updates
4. **Security**: Server-side checks are more secure

## Future Enhancements

### Option 1: Webhook Integration
Create a webhook from your app to Auth0 to sync additional roles:

```javascript
// Example webhook endpoint in your app
app.post('/webhooks/auth0/sync-roles', async (req, res) => {
  const { userId, additionalRoles } = req.body;
  
  // Update Auth0 user metadata
  await managementClient.updateUser({ id: userId }, {
    app_metadata: {
      additional_roles: additionalRoles
    }
  });
});
```

### Option 2: Refresh Token Strategy
Force token refresh when roles change:

```javascript
// In your app when roles change
await auth0.getAccessTokenSilently({ 
  ignoreCache: true,
  cacheMode: 'off' 
});
```

### Option 3: Custom Claims Action
Add a second Auth0 Action to fetch additional roles:

```javascript
exports.onExecutePostLogin = async (event, api) => {
  // Call your API to get additional roles
  const response = await fetch(`${YOUR_API}/roles/${event.user.user_id}`);
  const { additionalRoles } = await response.json();
  
  // Add to token
  api.idToken.setCustomClaim('additional_roles', additionalRoles);
};
```

## Testing

To test the Auth0 Action:

1. Use Auth0's Action testing interface
2. Provide sample event data:

```json
{
  "request": {
    "query": {
      "role": "job-seeker"
    }
  },
  "user": {
    "email": "<EMAIL>",
    "app_metadata": {}
  },
  "stats": {
    "logins_count": 1
  }
}
```

## Environment Variables

The following Auth0 configuration is stored in `.env.local`:

- `AUTH0_DOMAIN`: Your Auth0 domain
- `AUTH0_CLIENT_ID`: Application client ID
- `AUTH0_CLIENT_SECRET`: Application client secret
- `AUTH0_ISSUER_BASE_URL`: Full issuer URL
- `AUTH0_BASE_URL`: Your application's base URL

## Support

For Auth0-specific issues:
- Check Auth0 logs in the dashboard
- Enable Real-time Webtask Logs for debugging
- Use Auth0 community forums for help