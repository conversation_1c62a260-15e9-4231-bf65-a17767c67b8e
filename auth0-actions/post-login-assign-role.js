/**
 * Auth0 Action: Assign User Role
 * Trigger: Post Login
 * 
 * This action captures the role from login parameters and stores it in the user's
 * app_metadata and JWT tokens for secure role-based access control.
 * 
 * IMPORTANT: This action handles primary roles only. Additional roles (like REFERRAL_PARTNER)
 * are managed in the application database and checked server-side.
 */

exports.onExecutePostLogin = async (event, api) => {
  const namespace = 'https://kaleidotalent.com';

  // For new users, capture role from login flow
  if (event.stats.logins_count === 1 || !event.user.app_metadata?.role) {
    // Check multiple sources for role (in priority order)
    let role = null;
    
    // 1. Check authorization params (from login URL)
    if (event.request.query.role) {
      role = event.request.query.role;
    }
    // 2. Check state parameter (from Auth0 state)
    else if (event.transaction?.state?.role) {
      role = event.transaction.state.role;
    }
    // 3. Check user metadata (from signup)
    else if (event.user.user_metadata?.role) {
      role = event.user.user_metadata.role;
    }

    // Validate and save role
    if (role && ['employer', 'job-seeker', 'graduate', 'admin', 'super-admin', 'referral-partner'].includes(role)) {
      // Save to app_metadata (secure, non-editable by user)
      api.user.setAppMetadata('role', role);
      api.user.setAppMetadata('role_assigned_at', new Date().toISOString());
      
      console.log(`Assigned role ${role} to user ${event.user.email}`);
    }
  }

  // Always include current role in tokens
  const userRole = event.user.app_metadata?.role;

  if (userRole) {
    // Add to both ID and Access tokens with namespace
    api.idToken.setCustomClaim(`${namespace}/role`, userRole);
    api.accessToken.setCustomClaim(`${namespace}/role`, userRole);
    
    // Also add without namespace for easier access
    api.idToken.setCustomClaim('role', userRole);
    api.accessToken.setCustomClaim('role', userRole);
  }

  // Add other useful claims
  api.idToken.setCustomClaim(`${namespace}/email_verified`, event.user.email_verified);
  api.idToken.setCustomClaim(`${namespace}/logins_count`, event.stats.logins_count);
  api.idToken.setCustomClaim('logins_count', event.stats.logins_count);
  
  // Mark if this is a new user (first login)
  if (event.stats.logins_count === 1) {
    api.idToken.setCustomClaim(`${namespace}/is_new_user`, true);
    api.idToken.setCustomClaim('is_new_user', true);
  }

  // DUAL-ROLE SYSTEM NOTE:
  // Additional roles (like REFERRAL_PARTNER) are stored in the application database
  // and checked server-side using the RolesService.hasRole() method.
  // This ensures proper role synchronization and allows dynamic role assignment
  // without requiring JWT refresh.
  
  // Future Enhancement: Consider adding a webhook from the app to Auth0
  // to sync additional roles back to app_metadata for complete JWT-based auth.
};