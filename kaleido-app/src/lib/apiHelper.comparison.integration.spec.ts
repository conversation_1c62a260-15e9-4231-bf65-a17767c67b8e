import apiHelper from './apiHelper';
import {
  isWorkerTaskEndpoint,
  trackApiCall,
  debounceApiCall,
  WORKER_TASK_ENDPOINTS,
  WORKER_TASK_PATTERNS,
} from './apiHelper.utils';

describe('ApiHelper - Comparison Endpoint Integration', () => {
  // Suppress console.error for expected circuit breaker messages in tests
  const originalConsoleError = console.error;

  beforeAll(() => {
    console.error = jest.fn();
  });

  afterAll(() => {
    console.error = originalConsoleError;
  });
  describe('Worker Task Endpoint Detection', () => {
    it('should recognize comparison status endpoints as worker tasks', () => {
      const testEndpoints = [
        '/comparisons/status/job-123',
        '/comparisons/status/abc-def-ghi',
        '/comparisons/status/12345',
      ];

      testEndpoints.forEach(endpoint => {
        expect(isWorkerTaskEndpoint(endpoint)).toBe(true);
      });
    });

    it('should not recognize non-status comparison endpoints as worker tasks', () => {
      const nonWorkerEndpoints = [
        '/comparisons',
        '/comparisons/123',
        '/comparisons/jobs/123',
        '/comparisons/config/options',
        '/comparisons/queue/123/status', // Old pattern should not be recognized
      ];

      nonWorkerEndpoints.forEach(endpoint => {
        expect(isWorkerTaskEndpoint(endpoint)).toBe(false);
      });
    });

    it('should match comparison status pattern correctly', () => {
      const pattern = WORKER_TASK_PATTERNS.find(p => p.toString().includes('comparisons'));

      expect(pattern).toBeDefined();
      expect(pattern!.test('/comparisons/status/job-123')).toBe(true);
      expect(pattern!.test('/comparisons/status/abc')).toBe(true);
      expect(pattern!.test('/comparisons/queue/123/status')).toBe(false);
      expect(pattern!.test('/comparisons/123')).toBe(false);
    });

    it('should have comparison endpoint in WORKER_TASK_ENDPOINTS', () => {
      const hasComparisonEndpoint = WORKER_TASK_ENDPOINTS.some(endpoint =>
        endpoint.includes('comparisons/status/')
      );

      expect(hasComparisonEndpoint).toBe(true);
    });
  });

  describe('Circuit Breaker and Debouncing', () => {
    beforeEach(() => {
      // Clear any existing trackers
      jest.clearAllMocks();
    });

    it('should bypass circuit breaker for comparison polling endpoints', () => {
      const endpoint = '/comparisons/status/job-123';

      // Simulate multiple rapid calls
      for (let i = 0; i < 20; i++) {
        const allowed = trackApiCall(endpoint, 'GET');
        expect(allowed).toBe(true); // Should always allow polling endpoints
      }
    });

    it('should bypass debouncing for comparison polling endpoints', () => {
      const endpoint = '/comparisons/status/job-123';

      // Simulate multiple rapid calls
      for (let i = 0; i < 10; i++) {
        const allowed = debounceApiCall(endpoint, 'GET');
        expect(allowed).toBe(true); // Should always allow polling endpoints
      }
    });

    it('should apply circuit breaker to non-polling comparison endpoints', () => {
      const endpoint = '/comparisons';

      // First few calls should be allowed
      for (let i = 0; i < 5; i++) {
        trackApiCall(endpoint, 'GET');
      }

      // Simulate errors to trigger circuit breaker
      for (let i = 0; i < 6; i++) {
        trackApiCall(endpoint, 'GET', true);
      }

      // Next call should be blocked by circuit breaker
      const allowed = trackApiCall(endpoint, 'GET');
      expect(allowed).toBe(false);
    });
  });

  describe('Polling Request Configuration', () => {
    it('should mark comparison status requests as polling requests', async () => {
      // This test would normally make an actual request, but we'll mock it
      const mockGet = jest.spyOn(apiHelper, 'get').mockResolvedValue({
        status: 'processing',
        progress: 50,
      });

      await apiHelper.get('/comparisons/status/job-123', {
        isPollingRequest: true,
      });

      expect(mockGet).toHaveBeenCalledWith(
        '/comparisons/status/job-123',
        expect.objectContaining({
          isPollingRequest: true,
        })
      );

      mockGet.mockRestore();
    });
  });

  describe('Endpoint Pattern Consistency', () => {
    it('should follow the same pattern as other status endpoints', () => {
      const statusEndpoints = {
        scout: '/candidates/scout-status/job-123',
        videoJd: '/video-jd/status/job-123',
        comparison: '/comparisons/status/job-123',
        matchRank: '/jobs/match-rank-status/job-123',
        careerInsights: '/career-insights/status/job-123',
      };

      // All should be recognized as worker task endpoints
      Object.entries(statusEndpoints).forEach(([feature, endpoint]) => {
        expect(isWorkerTaskEndpoint(endpoint)).toBe(true);
      });

      // All should follow similar pattern: /{resource}/status/{id} or /{resource}/{action}-status/{id}
      Object.entries(statusEndpoints).forEach(([feature, endpoint]) => {
        expect(endpoint).toMatch(/^\/[a-z-]+\/([a-z-]+-status|status)\/[a-zA-Z0-9-]+$/);
      });
    });
  });

  describe('Real-world Polling Scenario', () => {
    it('should handle complete polling lifecycle without rate limiting', async () => {
      const jobId = 'test-job-123';
      const endpoint = `/comparisons/status/${jobId}`;

      // Mock API responses for different stages
      const mockResponses = [
        { status: 'queued', progress: 0 },
        { status: 'processing', progress: 25 },
        { status: 'processing', progress: 50 },
        { status: 'processing', progress: 75 },
        { status: 'completed', progress: 100, result: { comparisonId: 'comp-123' } },
      ];

      let callCount = 0;
      const mockGet = jest.spyOn(apiHelper, 'get').mockImplementation(() => {
        return Promise.resolve(mockResponses[callCount++ % mockResponses.length]);
      });

      // Simulate polling every 3 seconds for 5 iterations
      for (let i = 0; i < 5; i++) {
        const response = await apiHelper.get(endpoint, { isPollingRequest: true });

        // Verify the call was made successfully
        expect(mockGet).toHaveBeenCalledTimes(i + 1);
        expect(response).toEqual(mockResponses[i]);

        // Verify circuit breaker didn't block any calls
        const allowed = trackApiCall(endpoint, 'GET');
        expect(allowed).toBe(true);

        // Simulate 3-second delay between polls
        await new Promise(resolve => setTimeout(resolve, 100)); // Using shorter delay for tests
      }

      mockGet.mockRestore();
    });
  });
});
