import { renderHook, waitFor, act } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import React from 'react';
import { useUserRoles } from '../useUserRoles';
import { useUser } from '../useUser';
import { UserRole } from '@/types/roles';

// Mock useUser hook
jest.mock('../useUser');
const mockUseUser = useUser as jest.MockedFunction<typeof useUser>;

// Mock fetch
global.fetch = jest.fn();

describe('useUserRoles', () => {
  let queryClient: QueryClient;

  beforeEach(() => {
    queryClient = new QueryClient({
      defaultOptions: {
        queries: {
          retry: false,
        },
      },
    });

    // Clear localStorage
    localStorage.clear();

    // Reset fetch mock
    (fetch as jest.Mock).mockClear();
  });

  const wrapper = ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  );

  describe('Initial State', () => {
    it('should return loading state when user is not loaded', () => {
      mockUseUser.mockReturnValue({
        user: null,
        isLoading: true,
      } as any);

      const { result } = renderHook(() => useUserRoles(), { wrapper });

      expect(result.current.isLoading).toBe(true);
      expect(result.current.primaryRole).toBe(null);
      expect(result.current.additionalRoles).toEqual([]);
      expect(result.current.allRoles).toEqual([]);
    });

    it('should not fetch roles when user is not available', () => {
      mockUseUser.mockReturnValue({
        user: null,
        isLoading: false,
      } as any);

      renderHook(() => useUserRoles(), { wrapper });

      expect(fetch).not.toHaveBeenCalled();
    });
  });

  describe('Role Fetching', () => {
    const mockUser = {
      sub: 'user123',
      roles: [UserRole.JOB_SEEKER],
      email: '<EMAIL>',
    };

    beforeEach(() => {
      mockUseUser.mockReturnValue({
        user: mockUser,
        isLoading: false,
      } as any);
    });

    it('should fetch user roles from API', async () => {
      const mockRolesData = {
        primaryRole: UserRole.JOB_SEEKER,
        additionalRoles: [UserRole.REFERRAL_PARTNER],
        activeRole: UserRole.JOB_SEEKER,
      };

      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockRolesData,
      });

      const { result } = renderHook(() => useUserRoles(), { wrapper });

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      expect(fetch).toHaveBeenCalledWith('/api/auth/user-roles');
      expect(result.current.primaryRole).toBe(UserRole.JOB_SEEKER);
      expect(result.current.additionalRoles).toEqual([UserRole.REFERRAL_PARTNER]);
      expect(result.current.allRoles).toEqual([UserRole.JOB_SEEKER, UserRole.REFERRAL_PARTNER]);
    });

    it('should combine JWT roles with database roles', async () => {
      const mockUserWithMultipleRoles = {
        ...mockUser,
        roles: [UserRole.EMPLOYER, UserRole.ADMIN],
      };

      mockUseUser.mockReturnValue({
        user: mockUserWithMultipleRoles,
        isLoading: false,
      } as any);

      const mockRolesData = {
        primaryRole: UserRole.EMPLOYER,
        additionalRoles: [UserRole.REFERRAL_PARTNER],
        activeRole: UserRole.EMPLOYER,
      };

      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockRolesData,
      });

      const { result } = renderHook(() => useUserRoles(), { wrapper });

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      expect(result.current.allRoles).toEqual([
        UserRole.EMPLOYER,
        UserRole.ADMIN,
        UserRole.REFERRAL_PARTNER,
      ]);
    });
  });

  describe('Caching', () => {
    const mockUser = {
      sub: 'user123',
      roles: [UserRole.JOB_SEEKER],
    };

    beforeEach(() => {
      mockUseUser.mockReturnValue({
        user: mockUser,
        isLoading: false,
      } as any);
    });

    it('should use cached data if available and fresh', async () => {
      const mockRolesData = {
        primaryRole: UserRole.JOB_SEEKER,
        additionalRoles: [UserRole.REFERRAL_PARTNER],
        activeRole: UserRole.JOB_SEEKER,
      };

      const cacheKey = `userRoles_${mockUser.sub}`;
      localStorage.setItem(
        cacheKey,
        JSON.stringify({
          data: mockRolesData,
          timestamp: Date.now() - 1000, // 1 second ago
        })
      );

      const { result } = renderHook(() => useUserRoles(), { wrapper });

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      expect(fetch).not.toHaveBeenCalled();
      expect(result.current.additionalRoles).toEqual([UserRole.REFERRAL_PARTNER]);
    });

    it('should fetch new data if cache is stale', async () => {
      const cachedData = {
        primaryRole: UserRole.JOB_SEEKER,
        additionalRoles: [],
        activeRole: UserRole.JOB_SEEKER,
      };

      const freshData = {
        primaryRole: UserRole.JOB_SEEKER,
        additionalRoles: [UserRole.REFERRAL_PARTNER],
        activeRole: UserRole.JOB_SEEKER,
      };

      const cacheKey = `userRoles_${mockUser.sub}`;
      localStorage.setItem(
        cacheKey,
        JSON.stringify({
          data: cachedData,
          timestamp: Date.now() - 10 * 60 * 1000, // 10 minutes ago
        })
      );

      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => freshData,
      });

      const { result } = renderHook(() => useUserRoles(), { wrapper });

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      expect(fetch).toHaveBeenCalled();
      expect(result.current.additionalRoles).toEqual([UserRole.REFERRAL_PARTNER]);
    });
  });

  describe('Error Handling', () => {
    const mockUser = {
      sub: 'user123',
      roles: [UserRole.EMPLOYER],
    };

    beforeEach(() => {
      mockUseUser.mockReturnValue({
        user: mockUser,
        isLoading: false,
      } as any);
    });

    it('should fallback to JWT roles on server error', async () => {
      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: false,
        status: 500,
        json: async () => ({ error: 'Internal server error' }),
      });

      const { result } = renderHook(() => useUserRoles(), { wrapper });

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      expect(result.current.primaryRole).toBe(UserRole.EMPLOYER);
      expect(result.current.additionalRoles).toEqual([]);
      expect(result.current.allRoles).toEqual([UserRole.EMPLOYER]);
    });

    it('should fallback to JWT roles on rate limiting', async () => {
      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: false,
        status: 429,
        json: async () => ({ error: 'Rate limited' }),
      });

      const { result } = renderHook(() => useUserRoles(), { wrapper });

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      expect(result.current.primaryRole).toBe(UserRole.EMPLOYER);
      expect(result.current.error).toBeFalsy();
    });
  });

  describe('Role Checking Functions', () => {
    const mockUser = {
      sub: 'user123',
      roles: [UserRole.JOB_SEEKER],
    };

    beforeEach(() => {
      mockUseUser.mockReturnValue({
        user: mockUser,
        isLoading: false,
      } as any);
    });

    it('should correctly check hasRole', async () => {
      const mockRolesData = {
        primaryRole: UserRole.JOB_SEEKER,
        additionalRoles: [UserRole.REFERRAL_PARTNER],
        activeRole: UserRole.JOB_SEEKER,
      };

      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockRolesData,
      });

      const { result } = renderHook(() => useUserRoles(), { wrapper });

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      expect(result.current.hasRole(UserRole.JOB_SEEKER)).toBe(true);
      expect(result.current.hasRole(UserRole.REFERRAL_PARTNER)).toBe(true);
      expect(result.current.hasRole(UserRole.EMPLOYER)).toBe(false);
    });

    it('should correctly check hasAnyRole', async () => {
      const mockRolesData = {
        primaryRole: UserRole.JOB_SEEKER,
        additionalRoles: [UserRole.REFERRAL_PARTNER],
        activeRole: UserRole.JOB_SEEKER,
      };

      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockRolesData,
      });

      const { result } = renderHook(() => useUserRoles(), { wrapper });

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      expect(result.current.hasAnyRole([UserRole.EMPLOYER, UserRole.JOB_SEEKER])).toBe(true);
      expect(result.current.hasAnyRole([UserRole.EMPLOYER, UserRole.ADMIN])).toBe(false);
    });

    it('should correctly check hasAllRoles', async () => {
      const mockRolesData = {
        primaryRole: UserRole.JOB_SEEKER,
        additionalRoles: [UserRole.REFERRAL_PARTNER],
        activeRole: UserRole.JOB_SEEKER,
      };

      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockRolesData,
      });

      const { result } = renderHook(() => useUserRoles(), { wrapper });

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      expect(result.current.hasAllRoles([UserRole.JOB_SEEKER, UserRole.REFERRAL_PARTNER])).toBe(
        true
      );
      expect(result.current.hasAllRoles([UserRole.JOB_SEEKER, UserRole.EMPLOYER])).toBe(false);
    });
  });

  describe('Refetch Functionality', () => {
    const mockUser = {
      sub: 'user123',
      roles: [UserRole.JOB_SEEKER],
    };

    beforeEach(() => {
      mockUseUser.mockReturnValue({
        user: mockUser,
        isLoading: false,
      } as any);
    });

    it('should refetch roles when requested', async () => {
      const initialData = {
        primaryRole: UserRole.JOB_SEEKER,
        additionalRoles: [],
        activeRole: UserRole.JOB_SEEKER,
      };

      const updatedData = {
        primaryRole: UserRole.JOB_SEEKER,
        additionalRoles: [UserRole.REFERRAL_PARTNER],
        activeRole: UserRole.JOB_SEEKER,
      };

      (fetch as jest.Mock)
        .mockResolvedValueOnce({
          ok: true,
          json: async () => initialData,
        })
        .mockResolvedValueOnce({
          ok: true,
          json: async () => updatedData,
        });

      const { result } = renderHook(() => useUserRoles(), { wrapper });

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      expect(result.current.additionalRoles).toEqual([]);

      // Clear localStorage cache to ensure refetch hits the API
      localStorage.clear();

      // Refetch
      await act(async () => {
        await result.current.refetch();
      });

      await waitFor(() => {
        expect(result.current.additionalRoles).toEqual([UserRole.REFERRAL_PARTNER]);
      }, { timeout: 3000 });

      expect(fetch).toHaveBeenCalledTimes(2);
    });
  });
});
