import { useQuery } from '@tanstack/react-query';
import { UserRole } from '@/types/roles';
import { useUser } from './useUser';

interface UserRolesData {
  primaryRole: UserRole;
  additionalRoles: UserRole[];
  activeRole: UserRole;
}

export const useUserRoles = () => {
  const { user, isLoading: userLoading } = useUser();

  const {
    data: rolesData,
    isLoading: rolesLoading,
    error,
    refetch,
  } = useQuery<UserRolesData>({
    queryKey: ['userRoles', user?.sub],
    queryFn: async () => {
      if (!user?.sub) {
        throw new Error('No user ID available');
      }

      // Check for cached data first to prevent rate limiting
      const cacheKey = `userRoles_${user.sub}`;
      const cachedData = localStorage.getItem(cacheKey);

      if (cachedData) {
        try {
          const { data, timestamp } = JSON.parse(cachedData);
          const now = Date.now();

          // Use cache if less than 5 minutes old
          if (now - timestamp < 5 * 60 * 1000) {
            return data;
          }
        } catch (e) {
          // Ignore cache errors
        }
      }

      const response = await fetch('/api/auth/user-roles');

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));

        // If rate limited or server error, use JWT roles as fallback
        if (response.status === 500 || response.status === 429) {
          const fallbackData: UserRolesData = {
            primaryRole: (user.roles?.[0] as UserRole) || UserRole.JOB_SEEKER,
            additionalRoles: [],
            activeRole: (user.roles?.[0] as UserRole) || UserRole.JOB_SEEKER,
          };

          // Cache the fallback data
          localStorage.setItem(
            cacheKey,
            JSON.stringify({
              data: fallbackData,
              timestamp: Date.now(),
            })
          );

          return fallbackData;
        }

        throw new Error(errorData.error || 'Failed to fetch user roles');
      }

      const data = await response.json();

      // Cache the successful response
      localStorage.setItem(
        cacheKey,
        JSON.stringify({
          data,
          timestamp: Date.now(),
        })
      );

      return data;
    },
    enabled: !!user?.sub,
    staleTime: 10 * 60 * 1000, // Consider data fresh for 10 minutes
    gcTime: 60 * 60 * 1000, // Keep in cache for 1 hour
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    refetchOnReconnect: false,
    retry: (failureCount, error) => {
      // Don't retry on rate limiting or server errors
      if (error.message?.includes('500') || error.message?.includes('429')) {
        return false;
      }
      // Only retry once for other errors
      return failureCount < 1;
    },
    retryDelay: 5000,
  });

  // Combine JWT roles with database roles
  const allRoles = new Set<UserRole>();

  // Add JWT roles
  if (user?.roles) {
    user.roles.forEach((role: string) => allRoles.add(role as UserRole));
  }

  // Add primary role from database
  if (rolesData?.primaryRole) {
    allRoles.add(rolesData.primaryRole);
  }

  // Add additional roles from database
  if (rolesData?.additionalRoles) {
    rolesData.additionalRoles.forEach(role => allRoles.add(role));
  }

  return {
    user,
    primaryRole: rolesData?.primaryRole || (user?.roles?.[0] as UserRole) || null,
    additionalRoles: rolesData?.additionalRoles || [],
    activeRole:
      rolesData?.activeRole || rolesData?.primaryRole || (user?.roles?.[0] as UserRole) || null,
    allRoles: Array.from(allRoles),
    hasRole: (role: UserRole) => allRoles.has(role),
    hasAnyRole: (roles: UserRole[]) => roles.some(role => allRoles.has(role)),
    hasAllRoles: (roles: UserRole[]) => roles.every(role => allRoles.has(role)),
    isLoading: userLoading || rolesLoading,
    error,
    refetch,
  };
};

export default useUserRoles;
