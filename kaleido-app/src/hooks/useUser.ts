import { useUser as useAuth0User } from '@auth0/nextjs-auth0/client';
import { useQuery } from '@tanstack/react-query';
import { isPublicRoute } from '../types/publicRoutes';

// Cache duration for user data in milliseconds - increased to reduce API calls
const USER_CACHE_TIME = 30 * 60 * 1000; // 30 minutes
const USER_STALE_TIME = 15 * 60 * 1000; // 15 minutes

// Global tracking to prevent infinite loops
let lastCallTime = 0;
let callCount = 0;
const CALL_LIMIT = 5;
const TIME_WINDOW = 10000; // 10 seconds

// Create a no-op hook for public routes
const useNoOpUser = () => ({
  user: null,
  isLoading: false,
  error: null,
});

export const useUser = () => {
  // Check if we're on a public route - if so, skip Auth0 entirely
  const isOnPublicRoute =
    typeof window !== 'undefined' ? isPublicRoute(window.location.pathname) : false;

  // Always call both hooks to maintain hook order
  const auth0Result = useAuth0User();
  const noOpResult = useNoOpUser();

  // Use the appropriate result based on route type
  const {
    user: auth0User,
    isLoading: auth0Loading,
    error: auth0Error,
  } = isOnPublicRoute ? noOpResult : auth0Result;

  // Always call useQuery hook but disable it on public routes
  const {
    data: enrichedUser,
    isLoading: enrichedLoading,
    error: enrichedError,
  } = useQuery({
    queryKey: ['user', auth0User?.sub],
    queryFn: async () => {
      if (!auth0User?.sub) return null;

      // Check if we're on the candidates page and should skip auth calls
      if (typeof window !== 'undefined' && window.location.pathname.includes('/candidates')) {
        return auth0User;
      }

      // Infinite loop protection
      const now = Date.now();
      if (now - lastCallTime < TIME_WINDOW) {
        callCount++;
        if (callCount > CALL_LIMIT) {
          console.warn(
            '🚫 useUser: Too many calls detected, returning cached Auth0 user to prevent infinite loop'
          );
          return auth0User;
        }
      } else {
        callCount = 1;
        lastCallTime = now;
      }

      // Check for cached user data in localStorage first
      const cacheKey = `user_cache_${auth0User.sub}`;
      const cachedUserData = localStorage.getItem(cacheKey);

      if (cachedUserData) {
        try {
          const { data, timestamp } = JSON.parse(cachedUserData);
          const now = Date.now();

          // If cache is still fresh, use it
          if (now - timestamp < USER_STALE_TIME) {
            return data;
          }
        } catch (error) {
          console.error('Error parsing cached user data:', error);
          // Continue to fetch fresh data
        }
      }

      // Wait for auth session to be ready
      const authSessionReady = sessionStorage.getItem('auth_session_ready') === 'true';
      if (!authSessionReady) {
        return auth0User;
      }

      // Check for auth session
      const authSession = localStorage.getItem('auth_session');
      if (!authSession) {
        return auth0User;
      }

      try {
        // Use AbortController to handle timeout
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout

        const response = await fetch(`/api/auth/me`, {
          signal: controller.signal,
          headers: {
            Authorization: `Bearer ${JSON.parse(authSession).accessToken}`,
            'Content-Type': 'application/json',
          },
        });

        clearTimeout(timeoutId);

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}));

          // Check if we need to logout completely
          if (response.status === 401 && errorData.requiresLogout) {
            // Clear all auth data
            localStorage.clear();
            sessionStorage.clear();
            // Clear cookies
            document.cookie.split(';').forEach(cookie => {
              const eqPos = cookie.indexOf('=');
              const name = eqPos > -1 ? cookie.substr(0, eqPos).trim() : cookie.trim();
              document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/`;
            });
            // Redirect to logout
            window.location.href = '/api/auth/logout';
            return null;
          }

          if (response.status === 401 || response.status === 403) {
            // If unauthorized, clear auth session and return basic user
            localStorage.removeItem('auth_session');
            return auth0User;
          }
          throw new Error(`Failed to fetch user data: ${response.status}`);
        }

        const userData = await response.json();

        // Cache the response in localStorage
        try {
          localStorage.setItem(
            cacheKey,
            JSON.stringify({
              data: userData,
              timestamp: Date.now(),
            })
          );
        } catch (error) {
          console.error('Error caching user data:', error);
        }

        return userData;
      } catch (error) {
        console.error('Error fetching user data:', error);
        // If the error is a timeout (AbortError), log specific message
        if (error.name === 'AbortError') {
          console.warn('API request to backend /auth/me timed out');
        }
        // Return basic user data instead of failing
        return auth0User;
      }
    },
    enabled:
      !!auth0User?.sub &&
      !isOnPublicRoute &&
      !(typeof window !== 'undefined' && window.location.pathname.includes('/candidates')),
    // Keep cached data for 30 minutes (gcTime is the new name for cacheTime in React Query v4+)
    gcTime: USER_CACHE_TIME,
    // Consider data fresh for 15 minutes
    staleTime: USER_STALE_TIME,
    // Aggressive caching to prevent infinite loops
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    refetchOnReconnect: false,
    // Only retry once on failure
    retry: 1,
    retryDelay: 5000,
  });

  // If we're on a public route we still want to surface an authenticated user (if present)
  // but we avoid any extra enrichment/network calls. This lets pages such as /open-jobs
  // reflect the logged-in state while remaining fast for anonymous visitors.
  if (isOnPublicRoute) {
    return {
      user: auth0User || null,
      isLoading: auth0Loading,
      error: auth0Error || null,
    };
  }

  return {
    user: enrichedUser || auth0User,
    isLoading: auth0Loading || enrichedLoading,
    error: auth0Error || enrichedError,
  };
};

export default useUser;
