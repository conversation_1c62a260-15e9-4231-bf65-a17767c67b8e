import { useEffect, useState, useRef } from 'react';

import { UserRole } from '@/types/roles';
import apiHelper from '@/lib/apiHelper';
import { useUser } from '@auth0/nextjs-auth0/client';

interface ValidationResponse {
  isValid: boolean;
  missingFields: string[];
  mandatoryMissingFields: string[];
  hasCompletedOnboarding: boolean;
  completion: {
    overall: number;
    sections: {
      basicInfo: number;
      professionalInfo: number;
      preferences: number;
      additionalInfo: number;
    };
  };
  record: any;
}

interface ProactiveProfileValidationState {
  shouldShowAlert: boolean;
  validation: ValidationResponse | null;
  isLoading: boolean;
  dismissAlert: () => void;
  resetValidation: () => void;
}

/**
 * Hook to handle proactive profile validation alerts for job seekers
 * Shows IncompleteProfileAlert when users have incomplete profiles at strategic moments
 */
export const useProactiveProfileValidation = (
  enabledRoles: UserRole[] = [UserRole.JOB_SEEKER],
  options: {
    checkOnMount?: boolean;
    checkInterval?: number; // in milliseconds
    minCompletionThreshold?: number; // percentage below which to show alert
    dismissedStorageKey?: string;
  } = {}
): ProactiveProfileValidationState => {
  const { user, isLoading: isUserLoading } = useUser();
  const [shouldShowAlert, setShouldShowAlert] = useState(false);
  const [validation, setValidation] = useState<ValidationResponse | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [hasChecked, setHasChecked] = useState(false);
  const isValidatingRef = useRef(false);

  const {
    checkOnMount = true,
    checkInterval = 0, // 0 = no interval checking
    minCompletionThreshold = 70, // Show alert if completion < 70%
    dismissedStorageKey = 'profileAlertDismissed',
  } = options;

  // Check if the user has the required role
  const hasRequiredRole = () => {
    if (!user?.sub) return false;

    try {
      const cachedRoleData = localStorage.getItem(`userRole_${user.sub}`);
      if (cachedRoleData) {
        const { role } = JSON.parse(cachedRoleData);
        return enabledRoles.includes(role);
      }
    } catch (error) {
      console.error('Error checking cached role:', error);
    }

    return false;
  };

  // Check if alert was dismissed recently
  const isAlertDismissed = () => {
    if (!user?.sub) return false;

    try {
      const dismissedData = localStorage.getItem(`${dismissedStorageKey}_${user.sub}`);
      if (dismissedData) {
        const { timestamp } = JSON.parse(dismissedData);
        const dismissedTime = new Date(timestamp).getTime();
        const currentTime = Date.now();
        const dayInMs = 24 * 60 * 60 * 1000;

        // Show alert again after 24 hours
        return currentTime - dismissedTime < dayInMs;
      }
    } catch (error) {
      console.error('Error checking dismissed alert status:', error);
    }

    return false;
  };

  // Validate profile completion
  const validateProfile = async (): Promise<ValidationResponse | null> => {
    if (!user?.sub || !hasRequiredRole() || isAlertDismissed()) {
      return null;
    }

    // Prevent concurrent requests
    if (isValidatingRef.current) {
      return null;
    }
    isValidatingRef.current = true;

    setIsLoading(true);

    try {
      // For job seekers, get the profile which includes validation data
      const response = await apiHelper.get('/job-seekers');

      if (response?.validation) {
        const validationData = response.validation;

        // Only show alert for users who haven't completed onboarding
        // Users who have completed onboarding should not be bothered with profile completion alerts
        if (response.hasCompletedOnboarding) {
          return null;
        }

        // For users still in onboarding, check if profile needs attention
        const needsAttention =
          !validationData.isValid ||
          validationData.completion.overall < minCompletionThreshold ||
          validationData.mandatoryMissingFields.length > 0;

        if (needsAttention) {
          return validationData;
        }
      }

      return null;
    } catch (error) {
      console.error('Error validating profile:', error);
      return null;
    } finally {
      setIsLoading(false);
      isValidatingRef.current = false;
    }
  };

  // Dismiss alert and store preference
  const dismissAlert = () => {
    setShouldShowAlert(false);

    if (user?.sub) {
      const dismissedData = {
        timestamp: new Date().toISOString(),
        userId: user.sub,
      };

      localStorage.setItem(`${dismissedStorageKey}_${user.sub}`, JSON.stringify(dismissedData));
    }
  };

  // Reset validation state
  const resetValidation = () => {
    setValidation(null);
    setShouldShowAlert(false);
    setHasChecked(false);

    if (user?.sub) {
      localStorage.removeItem(`${dismissedStorageKey}_${user.sub}`);
    }
  };

  // Initial validation check on mount
  useEffect(() => {
    const performInitialCheck = async () => {
      if (
        !isUserLoading &&
        user?.sub &&
        hasRequiredRole() &&
        checkOnMount &&
        !hasChecked &&
        !isAlertDismissed()
      ) {
        const validationResult = await validateProfile();

        if (validationResult) {
          setValidation(validationResult);
          setShouldShowAlert(true);
        }

        setHasChecked(true);
      }
    };

    performInitialCheck();
  }, [user, isUserLoading, hasChecked, checkOnMount]);

  // Interval checking (if enabled)
  useEffect(() => {
    if (checkInterval > 0 && hasRequiredRole() && !isAlertDismissed()) {
      const intervalId = setInterval(async () => {
        const validationResult = await validateProfile();

        if (validationResult && !shouldShowAlert) {
          setValidation(validationResult);
          setShouldShowAlert(true);
        }
      }, checkInterval);

      return () => clearInterval(intervalId);
    }
  }, [checkInterval, shouldShowAlert]);

  return {
    shouldShowAlert: shouldShowAlert && !!validation && hasRequiredRole(),
    validation,
    isLoading,
    dismissAlert,
    resetValidation,
  };
};

export default useProactiveProfileValidation;
