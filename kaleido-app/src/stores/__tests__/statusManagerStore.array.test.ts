import { renderHook, act } from '@testing-library/react';
import { useStatusManagerStore } from '../statusManagerStore';

describe('statusManagerStore - Array-based completedJobs', () => {
  beforeEach(() => {
    // Reset store state before each test
    const { result } = renderHook(() => useStatusManagerStore());
    act(() => {
      result.current.reset();
    });
  });

  describe('completedJobs as array', () => {
    it('should initialize completedJobs as an empty array', () => {
      const { result } = renderHook(() => useStatusManagerStore());

      expect(result.current.completedJobs).toEqual([]);
      expect(Array.isArray(result.current.completedJobs)).toBe(true);
    });

    it('should add job ID to completedJobs array when marking as completed', () => {
      const { result } = renderHook(() => useStatusManagerStore());

      // Create an instance with a job
      act(() => {
        result.current.createInstance('test-instance', 'test-type', [
          {
            id: 'job1',
            type: 'test-type',
            status: 'processing',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          },
        ]);
      });

      // Mark job as completed
      act(() => {
        result.current.markJobCompleted('test-instance', 'job1', { data: 'result' });
      });

      expect(result.current.completedJobs).toContain('job1');
      expect(result.current.completedJobs.length).toBe(1);
    });

    it('should not add duplicate job IDs to completedJobs', () => {
      const { result } = renderHook(() => useStatusManagerStore());

      // Create an instance with a job
      act(() => {
        result.current.createInstance('test-instance', 'test-type', [
          {
            id: 'job1',
            type: 'test-type',
            status: 'processing',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          },
        ]);
      });

      // Mark job as completed multiple times
      act(() => {
        result.current.markJobCompleted('test-instance', 'job1', { data: 'result' });
        result.current.markJobCompleted('test-instance', 'job1', { data: 'result2' });
      });

      expect(result.current.completedJobs).toEqual(['job1']);
      expect(result.current.completedJobs.length).toBe(1);
    });

    it('should check if job is completed using array includes', () => {
      const { result } = renderHook(() => useStatusManagerStore());

      // Create an instance with jobs
      act(() => {
        result.current.createInstance('test-instance', 'test-type', [
          {
            id: 'job1',
            type: 'test-type',
            status: 'processing',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          },
        ]);
      });

      // Initially not completed
      expect(result.current.isJobCompleted('job1')).toBe(false);

      // Mark as completed
      act(() => {
        result.current.markJobCompleted('test-instance', 'job1');
      });

      // Now should be completed
      expect(result.current.isJobCompleted('job1')).toBe(true);
      expect(result.current.isJobCompleted('job2')).toBe(false);
    });

    it('should clear completedJobs array when clearCompletionCache is called', () => {
      const { result } = renderHook(() => useStatusManagerStore());

      // Create an instance and complete some jobs
      act(() => {
        result.current.createInstance('test-instance', 'test-type', [
          {
            id: 'job1',
            type: 'test-type',
            status: 'processing',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          },
          {
            id: 'job2',
            type: 'test-type',
            status: 'processing',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          },
        ]);

        result.current.markJobCompleted('test-instance', 'job1');
        result.current.markJobCompleted('test-instance', 'job2');
      });

      expect(result.current.completedJobs.length).toBe(2);

      // Clear cache
      act(() => {
        result.current.clearCompletionCache();
      });

      expect(result.current.completedJobs).toEqual([]);
      expect(result.current.completedJobs.length).toBe(0);
    });

    it('should reset completedJobs to empty array on store reset', () => {
      const { result } = renderHook(() => useStatusManagerStore());

      // Add some completed jobs
      act(() => {
        result.current.createInstance('test-instance', 'test-type', [
          {
            id: 'job1',
            type: 'test-type',
            status: 'processing',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          },
        ]);
        result.current.markJobCompleted('test-instance', 'job1');
      });

      expect(result.current.completedJobs.length).toBeGreaterThan(0);

      // Reset store
      act(() => {
        result.current.reset();
      });

      expect(result.current.completedJobs).toEqual([]);
      expect(Array.isArray(result.current.completedJobs)).toBe(true);
    });

    it('should handle multiple completed jobs correctly', () => {
      const { result } = renderHook(() => useStatusManagerStore());

      // Create instance with multiple jobs
      act(() => {
        result.current.createInstance('test-instance', 'test-type', [
          {
            id: 'job1',
            type: 'test-type',
            status: 'processing',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          },
          {
            id: 'job2',
            type: 'test-type',
            status: 'processing',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          },
          {
            id: 'job3',
            type: 'test-type',
            status: 'processing',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          },
        ]);
      });

      // Complete jobs in sequence
      act(() => {
        result.current.markJobCompleted('test-instance', 'job1');
      });
      expect(result.current.completedJobs).toEqual(['job1']);

      act(() => {
        result.current.markJobCompleted('test-instance', 'job3');
      });
      expect(result.current.completedJobs).toEqual(['job1', 'job3']);

      act(() => {
        result.current.markJobCompleted('test-instance', 'job2');
      });
      expect(result.current.completedJobs).toEqual(['job1', 'job3', 'job2']);

      // Verify all are marked as completed
      expect(result.current.isJobCompleted('job1')).toBe(true);
      expect(result.current.isJobCompleted('job2')).toBe(true);
      expect(result.current.isJobCompleted('job3')).toBe(true);
    });
  });

  describe('Integration with Immer', () => {
    it('should not throw Immer MapSet error when using array operations', () => {
      const { result } = renderHook(() => useStatusManagerStore());

      // This should not throw any Immer-related errors
      expect(() => {
        act(() => {
          result.current.createInstance('test-instance', 'test-type', [
            {
              id: 'job1',
              type: 'test-type',
              status: 'processing',
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString(),
            },
          ]);
          result.current.markJobCompleted('test-instance', 'job1');
          result.current.clearCompletionCache();
        });
      }).not.toThrow();
    });

    it('should maintain immutability with array operations', () => {
      const { result } = renderHook(() => useStatusManagerStore());

      act(() => {
        result.current.createInstance('test-instance', 'test-type', [
          {
            id: 'job1',
            type: 'test-type',
            status: 'processing',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          },
        ]);
      });

      const originalArray = result.current.completedJobs;

      act(() => {
        result.current.markJobCompleted('test-instance', 'job1');
      });

      // Should be a new array reference
      expect(result.current.completedJobs).not.toBe(originalArray);
    });
  });
});
