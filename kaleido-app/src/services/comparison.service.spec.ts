import { comparisonService } from './comparison.service';
import apiHelper from '@/lib/apiHelper';
import { CreateComparisonDto, ComparisonType } from '@/types/comparison.types';

// Mock the apiHelper module
jest.mock('@/lib/apiHelper', () => ({
  __esModule: true,
  default: {
    get: jest.fn(),
    post: jest.fn(),
    delete: jest.fn(),
  },
}));

describe('ComparisonService', () => {
  const mockApiHelper = apiHelper as jest.Mocked<typeof apiHelper>;

  // Suppress console.error for expected errors in tests
  const originalConsoleError = console.error;

  beforeAll(() => {
    console.error = jest.fn();
  });

  afterAll(() => {
    console.error = originalConsoleError;
  });

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('createComparison', () => {
    it('should create a comparison and return job info', async () => {
      const mockData: CreateComparisonDto = {
        jobId: 'job-123',
        candidateIds: ['candidate-1', 'candidate-2'],
        comparisonType: ComparisonType.QUICK_OVERVIEW,
        userPrompt: 'Compare these candidates',
      };

      const mockResponse = {
        id: 'queue-job-123',
        jobId: 'comparison-123',
        status: 'queued',
        message: 'Comparison job created successfully',
      };

      mockApiHelper.post.mockResolvedValue(mockResponse);

      const result = await comparisonService.createComparison(mockData);

      expect(mockApiHelper.post).toHaveBeenCalledWith('/comparisons', mockData);
      expect(result).toEqual(mockResponse);
      expect(result.id).toBe('queue-job-123');
      expect(result.jobId).toBe('comparison-123');
    });

    it('should handle errors when creating comparison', async () => {
      const mockData: CreateComparisonDto = {
        jobId: 'job-123',
        candidateIds: ['candidate-1'],
        comparisonType: ComparisonType.DETAILED_ANALYSIS,
      };

      mockApiHelper.post.mockRejectedValue(new Error('API Error'));

      await expect(comparisonService.createComparison(mockData)).rejects.toThrow(
        'Failed to create comparison'
      );
    });
  });

  describe('getComparisonJobStatus', () => {
    it('should get comparison job status using the correct endpoint pattern', async () => {
      const jobId = 'queue-job-123';
      const mockStatus = {
        jobId: 'queue-job-123',
        status: 'processing',
        progress: 65,
        message: 'Analyzing candidates...',
      };

      mockApiHelper.get.mockResolvedValue(mockStatus);

      const result = await comparisonService.getComparisonJobStatus(jobId);

      expect(mockApiHelper.get).toHaveBeenCalledWith(`/comparisons/status/${jobId}`);
      expect(result).toEqual(mockStatus);
      expect(result.progress).toBe(65);
    });

    it('should handle completed status with results', async () => {
      const jobId = 'queue-job-123';
      const mockStatus = {
        jobId: 'queue-job-123',
        status: 'completed',
        progress: 100,
        result: {
          comparisonId: 'comparison-123',
          executiveSummary: 'Based on the analysis...',
          recommendations: {
            topChoice: {
              candidateId: 'candidate-1',
              candidateName: 'John Doe',
              reasoning: 'Best technical skills and cultural fit',
            },
          },
          candidateAnalysis: {
            'John Doe': {
              scores: {
                skills: 90,
                experience: 85,
                culturalFit: 88,
              },
              strengths: ['Strong technical background', 'Leadership experience'],
              weaknesses: ['Limited remote work experience'],
            },
            'Jane Smith': {
              scores: {
                skills: 85,
                experience: 90,
                culturalFit: 82,
              },
              strengths: ['Extensive industry experience', 'Strong communication'],
              weaknesses: ['May be overqualified'],
            },
          },
        },
      };

      mockApiHelper.get.mockResolvedValue(mockStatus);

      const result = await comparisonService.getComparisonJobStatus(jobId);

      expect(result.status).toBe('completed');
      expect(result.result.candidateAnalysis).toHaveProperty('John Doe');
      expect(result.result.candidateAnalysis).toHaveProperty('Jane Smith');
    });

    it('should handle failed status', async () => {
      const jobId = 'queue-job-123';
      const mockStatus = {
        jobId: 'queue-job-123',
        status: 'failed',
        progress: 0,
        error: 'Failed to analyze candidates',
      };

      mockApiHelper.get.mockResolvedValue(mockStatus);

      const result = await comparisonService.getComparisonJobStatus(jobId);

      expect(result.status).toBe('failed');
      expect(result.error).toBeDefined();
    });

    it('should handle errors when getting status', async () => {
      mockApiHelper.get.mockRejectedValue(new Error('Network error'));

      await expect(comparisonService.getComparisonJobStatus('invalid-id')).rejects.toThrow(
        'Failed to fetch comparison job status'
      );
    });
  });

  describe('getComparison', () => {
    it('should get comparison details', async () => {
      const comparisonId = 'comparison-123';
      const mockComparison = {
        id: comparisonId,
        jobId: 'job-123',
        candidateIds: ['candidate-1', 'candidate-2'],
        comparisonType: 'quick_overview',
        status: 'completed',
        comparisonResults: {
          executiveSummary: 'Summary of comparison',
          recommendations: {},
        },
      };

      mockApiHelper.get.mockResolvedValue(mockComparison);

      const result = await comparisonService.getComparison(comparisonId);

      expect(mockApiHelper.get).toHaveBeenCalledWith(`/comparisons/${comparisonId}`);
      expect(result).toEqual(mockComparison);
    });
  });

  describe('getJobComparisons', () => {
    it('should get all comparisons for a job', async () => {
      const jobId = 'job-123';
      const mockComparisons = [
        {
          id: 'comparison-1',
          jobId,
          status: 'completed',
          createdAt: '2024-01-01T00:00:00Z',
        },
        {
          id: 'comparison-2',
          jobId,
          status: 'processing',
          createdAt: '2024-01-02T00:00:00Z',
        },
      ];

      mockApiHelper.get.mockResolvedValue(mockComparisons);

      const result = await comparisonService.getJobComparisons(jobId);

      expect(mockApiHelper.get).toHaveBeenCalledWith(`/comparisons/jobs/${jobId}`);
      expect(result).toHaveLength(2);
      expect(result[0].jobId).toBe(jobId);
    });
  });

  describe('deleteComparison', () => {
    it('should delete a comparison', async () => {
      const comparisonId = 'comparison-123';
      mockApiHelper.delete.mockResolvedValue(undefined);

      await comparisonService.deleteComparison(comparisonId);

      expect(mockApiHelper.delete).toHaveBeenCalledWith(`/comparisons/${comparisonId}`);
    });

    it('should handle errors when deleting', async () => {
      mockApiHelper.delete.mockRejectedValue(new Error('Not found'));

      await expect(comparisonService.deleteComparison('invalid-id')).rejects.toThrow(
        'Failed to delete comparison'
      );
    });
  });

  describe('polling pattern validation', () => {
    it('should use the correct status endpoint pattern for polling', async () => {
      // This test ensures the endpoint follows the same pattern as scout
      const jobId = 'test-job-123';
      mockApiHelper.get.mockResolvedValue({ status: 'processing' });

      await comparisonService.getComparisonJobStatus(jobId);

      const calledEndpoint = mockApiHelper.get.mock.calls[0][0];

      // Verify it matches the pattern: /comparisons/status/{jobId}
      expect(calledEndpoint).toMatch(/^\/comparisons\/status\/[a-zA-Z0-9-]+$/);
      expect(calledEndpoint).toBe(`/comparisons/status/${jobId}`);

      // Verify it doesn't use the old pattern
      expect(calledEndpoint).not.toContain('/queue/');
      expect(calledEndpoint).not.toContain('/status/status');
    });
  });
});
