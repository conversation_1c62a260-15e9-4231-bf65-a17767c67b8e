'use client';

import { useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import { referralApi } from '../services/referralApi';

interface ReferralTrackerProps {
  candidateId?: string;
  jobId?: string;
}

export function ReferralTracker({ candidateId, jobId }: ReferralTrackerProps) {
  const searchParams = useSearchParams();
  const referralCode = searchParams.get('ref');

  useEffect(() => {
    const trackReferral = async () => {
      // Check if we have a referral code in URL params
      if (referralCode) {
        // Store in session storage for later use
        sessionStorage.setItem('referralCode', referralCode);

        // Send tracking event
        await referralApi.captureReferral({
          referralCode,
          source: 'website',
          medium: 'referral',
        });
      }

      // When candidate applies for a job, create the referral record
      if (candidateId && jobId) {
        const storedCode = sessionStorage.getItem('referralCode');
        if (storedCode) {
          try {
            await referralApi.trackReferral({
              referralCode: storedCode,
              candidateId,
              jobId,
              trackingData: {
                source: 'website',
                medium: 'referral',
              },
            });

            // Clear the referral code after successful tracking
            sessionStorage.removeItem('referralCode');
          } catch (error) {
            console.error('Failed to track referral:', error);
          }
        }
      }
    };

    trackReferral();
  }, [referralCode, candidateId, jobId]);

  return null; // This is a tracking component, doesn't render anything
}
