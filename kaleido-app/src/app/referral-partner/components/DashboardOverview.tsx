'use client';

import React from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { DollarSign, Users, TrendingUp, Percent } from 'lucide-react';
import { DashboardData } from '../types';

interface DashboardOverviewProps {
  data: DashboardData;
}

export const DashboardOverview: React.FC<DashboardOverviewProps> = ({ data }) => {
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const stats = [
    {
      title: 'Total Earnings',
      value: formatCurrency(data.earnings.total),
      icon: DollarSign,
      description: 'All time earnings',
      color: 'text-green-600',
    },
    {
      title: 'Pending Earnings',
      value: formatCurrency(data.earnings.pending),
      icon: TrendingUp,
      description: 'Awaiting payment',
      color: 'text-yellow-600',
    },
    {
      title: 'Total Referrals',
      value: data.metrics?.totalReferrals || 0,
      icon: Users,
      description: 'Candidates referred',
      color: 'text-blue-600',
    },
    {
      title: 'Conversion Rate',
      value: `${data.metrics?.conversionRate?.toFixed(1) || 0}%`,
      icon: Percent,
      description: 'Success rate',
      color: 'text-purple-600',
    },
  ];

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      {stats.map((stat, index) => (
        <Card key={index}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{stat.title}</CardTitle>
            <stat.icon className={`h-4 w-4 ${stat.color}`} />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stat.value}</div>
            <p className="text-xs text-muted-foreground mt-1">{stat.description}</p>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};
