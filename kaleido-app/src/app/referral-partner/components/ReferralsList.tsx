'use client';

import React from 'react';
import { Badge } from '@/components/ui/badge';
import GenericTable, { GenericTableProps } from '@/components/Layouts/GenericTable';
import { Referral, ReferralStatus, ReferralPartner } from '../types';
import { Hash, TrendingUp, Calendar, CheckCircle, DollarSign, Clock } from 'lucide-react';

interface ReferralsListProps {
  partner: ReferralPartner;
  onRefresh?: () => void;
}

const statusColors: Record<ReferralStatus, string> = {
  [ReferralStatus.PENDING]: 'bg-gray-500',
  [ReferralStatus.CANDIDATE_APPLIED]: 'bg-blue-500',
  [ReferralStatus.CANDIDATE_INTERVIEWED]: 'bg-yellow-500',
  [ReferralStatus.CANDIDATE_HIRED]: 'bg-green-500',
  [ReferralStatus.BOUNTY_APPROVED]: 'bg-purple-500',
  [ReferralStatus.BOUNTY_PAID]: 'bg-green-700',
  [ReferralStatus.EXPIRED]: 'bg-red-500',
  [ReferralStatus.CANCELLED]: 'bg-red-700',
};

const formatStatus = (status: ReferralStatus): string => {
  return status
    .replace(/_/g, ' ')
    .toLowerCase()
    .split(' ')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
};

export const ReferralsList: React.FC<ReferralsListProps> = ({ partner, onRefresh }) => {
  const referrals = partner.referrals || [];
  const formatDate = (dateString?: string) => {
    if (!dateString) return '-';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const formatCurrency = (amount?: number) => {
    if (!amount) return '-';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const columns = [
    {
      key: 'referralCode',
      label: 'Referral Code',
      icon: Hash,
      render: (value: string) => (
        <span className="font-mono text-sm text-purple-600 dark:text-purple-400">{value}</span>
      ),
    },
    {
      key: 'status',
      label: 'Status',
      icon: TrendingUp,
      render: (value: ReferralStatus) => (
        <Badge className={`${statusColors[value]} text-white`}>{formatStatus(value)}</Badge>
      ),
    },
    {
      key: 'candidateAppliedAt',
      label: 'Applied Date',
      icon: Calendar,
      render: (value: string) => formatDate(value),
    },
    {
      key: 'candidateHiredAt',
      label: 'Hired Date',
      icon: CheckCircle,
      render: (value: string) => formatDate(value),
    },
    {
      key: 'bountyAmount',
      label: 'Bounty Amount',
      icon: DollarSign,
      render: (value: number) => (
        <span className="font-semibold text-green-600 dark:text-green-400">
          {formatCurrency(value)}
        </span>
      ),
    },
    {
      key: 'createdAt',
      label: 'Created',
      icon: Clock,
      render: (value: string) => formatDate(value),
    },
  ];

  return (
    <div className="bg-white/5 backdrop-blur-md rounded-lg border border-white/10 shadow-xl p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white flex items-center gap-2">
          <TrendingUp className="h-6 w-6 text-purple-600 dark:text-purple-400" />
          Your Referrals
        </h2>
        <p className="text-gray-600 dark:text-gray-400 mt-1">
          Track all your referral submissions and their current status
        </p>
      </div>

      <GenericTable
        data={referrals}
        columns={columns}
        itemsPerPage={10}
        emptyStateConfig={{
          type: 'generic',
          title: 'No referrals yet',
          description: 'Start sharing your referral link to earn commissions',
          showButton: false,
        }}
        searchPlaceholder="Search by referral code..."
        disablePagination={referrals.length <= 10}
      />
    </div>
  );
};
