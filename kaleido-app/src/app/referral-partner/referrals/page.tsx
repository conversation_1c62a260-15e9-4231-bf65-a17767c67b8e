'use client';

import React, { useEffect, useState } from 'react';
import { useUser } from '@auth0/nextjs-auth0/client';
import AppLayout from '@/components/steps/layout/AppLayout';
import { ReferralsList } from '../components/ReferralsList';
import { ReferralPartnerTabs } from '../components/ReferralPartnerTabs';
import { referralApi } from '../services/referralApi';
import { ReferralPartner } from '../types';
import { Card, CardContent } from '@/components/ui/card';
import ReferralPageHeader from '@/components/common/ReferralPageHeader';
import { FileText } from 'lucide-react';

export default function ReferralsPage() {
  const { user, isLoading: userLoading } = useUser();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [partner, setPartner] = useState<ReferralPartner | null>(null);

  const loadPartnerData = async () => {
    try {
      setLoading(true);
      setError(null);

      if (!user?.sub) {
        setError('User not authenticated.');
        return;
      }

      const partnerResponse = await referralApi.getPartnerByClientId(user.sub);

      if (!partnerResponse) {
        setError('No referral partner profile found for your account.');
        return;
      }

      setPartner(partnerResponse);
    } catch (err) {
      console.error('Error loading partner data:', err);
      setError('Failed to load partner data. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (user && !userLoading) {
      loadPartnerData();
    }
  }, [user, userLoading]);

  return (
    <AppLayout isLoading={loading || userLoading}>
      {error || !partner ? (
        <div className="relative flex h-full w-full items-center justify-center p-3">
          <Card className="max-w-md border-white/5 bg-red-900/10">
            <CardContent className="p-8 text-center">
              <p className="text-white/80">{error || 'Unable to load partner data.'}</p>
            </CardContent>
          </Card>
        </div>
      ) : (
        <div className="h-full flex flex-col relative">
          {/* Hero Header */}
          <ReferralPageHeader
            title="Referral Details"
            description="Track and manage all your referral submissions"
            icon={FileText}
          />

          {/* Spacer for fixed header */}
          <div className="h-[320px] flex-shrink-0"></div>

          {/* Tab Navigation */}
          <div className="sticky top-0 z-40 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 border-b border-white/5">
            <div className="container mx-auto px-4 sm:px-6 h-14">
              <ReferralPartnerTabs className="h-full" />
            </div>
          </div>

          {/* Content */}
          <div className="flex-1 container mx-auto px-4 sm:px-6 py-8">
            <ReferralsList partner={partner} onRefresh={loadPartnerData} />
          </div>
        </div>
      )}
    </AppLayout>
  );
}