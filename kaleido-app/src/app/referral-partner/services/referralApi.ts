import {
  DashboardData,
  Referral,
  ReferralEarnings,
  ReferralPartner,
  ReferralStatus,
} from '../types';
import apiHelper from '@/lib/apiHelper';

class ReferralApiService {
  // Referral Partner endpoints
  async getPartnerByClientId(clientId: string): Promise<ReferralPartner | null> {
    try {
      // Check if partner exists using public endpoint
      const response = await apiHelper.post<{ exists: boolean; partner?: ReferralPartner }>(
        '/referral-partners/public/check',
        {},
        {
          headers: {
            'x-client-id': clientId,
          },
        }
      );

      if (response.exists && response.partner) {
        return response.partner;
      }

      return null;
    } catch (error) {
      console.error('Error fetching partner by clientId:', error);
      return null;
    }
  }

  async getPartnerById(id: string): Promise<ReferralPartner> {
    return apiHelper.get<ReferralPartner>(`/referral-partners/${id}`);
  }

  async getDashboard(partnerId: string): Promise<DashboardData> {
    return apiHelper.get<DashboardData>(`/referral-partners/${partnerId}/dashboard`);
  }

  async updatePartner(id: string, data: Partial<ReferralPartner>): Promise<ReferralPartner> {
    return apiHelper.put<ReferralPartner>(`/referral-partners/${id}`, data);
  }

  // Referral endpoints
  async getReferrals(filters?: {
    referralPartnerId?: string;
    status?: ReferralStatus;
    candidateId?: string;
    jobId?: string;
  }): Promise<Referral[]> {
    return apiHelper.get<Referral[]>('/referrals', { params: filters });
  }

  async getReferralById(id: string): Promise<Referral> {
    return apiHelper.get<Referral>(`/referrals/${id}`);
  }

  async trackReferral(data: {
    referralCode: string;
    candidateId: string;
    jobId: string;
    trackingData?: any;
  }): Promise<Referral> {
    return apiHelper.post<Referral>('/referrals/track', data);
  }

  // Earnings endpoints
  async getEarnings(partnerId: string): Promise<ReferralEarnings> {
    return apiHelper.get<ReferralEarnings>(`/referral-partners/${partnerId}/earnings`);
  }

  async requestPayment(partnerId: string, amount: number, paymentMethod?: string): Promise<any> {
    return apiHelper.post(`/referral-partners/${partnerId}/request-payment`, {
      amount,
      paymentMethod,
    });
  }

  async getSummaryReport(partnerId: string): Promise<any> {
    return apiHelper.get(`/referral-partners/${partnerId}/reports/summary`);
  }

  async getDetailedReport(partnerId: string): Promise<any> {
    return apiHelper.get(`/referral-partners/${partnerId}/reports/detailed`);
  }

  // Public endpoints (no auth required)
  async captureReferral(data: {
    referralCode: string;
    source?: string;
    medium?: string;
    campaign?: string;
  }): Promise<{ success: boolean }> {
    // This is a public endpoint, use skipAuth option
    return apiHelper.post<{ success: boolean }>('/referrals/capture', data, { skipAuth: true });
  }

  // Job endpoints
  async getAvailableJobs(params?: {
    limit?: number;
    offset?: number;
    sort?: string;
    order?: 'ASC' | 'DESC';
    search?: string;
    department?: string;
    location?: string;
  }): Promise<any> {
    return apiHelper.get('/referrals/jobs/available', { params });
  }

  // Stripe Connect endpoints
  async createStripeConnectAccount(partnerId: string): Promise<{ url: string; accountId: string }> {
    return apiHelper.post(`/referral-partners/${partnerId}/stripe/connect`, {});
  }

  async getStripeAccountStatus(
    partnerId: string
  ): Promise<{ connected: boolean; accountId?: string }> {
    return apiHelper.get(`/referral-partners/${partnerId}/stripe/status`);
  }

  async createStripeAccountLink(partnerId: string): Promise<{ url: string }> {
    return apiHelper.post(`/referral-partners/${partnerId}/stripe/account-link`, {});
  }

  // Activation endpoints
  async activateForJobSeeker(): Promise<ReferralPartner> {
    const result = await apiHelper.post<ReferralPartner>('/referral/activation/job-seeker', {});
    // Clear the cache after activation
    localStorage.removeItem('referralPartnerStatus');
    return result;
  }

  async activateForCompany(): Promise<ReferralPartner> {
    const result = await apiHelper.post<ReferralPartner>('/referral/activation/company', {});
    // Clear the cache after activation
    localStorage.removeItem('referralPartnerStatus');
    return result;
  }

  async deactivateReferralPartner(): Promise<void> {
    await apiHelper.delete('/referral/activation/deactivate');
    // Clear the cache after deactivation
    localStorage.removeItem('referralPartnerStatus');
  }

  async checkReferralStatus(): Promise<{ isActive: boolean; partner?: ReferralPartner }> {
    // Check cache first to prevent repeated API calls
    const cacheKey = 'referralPartnerStatus';
    const cached = localStorage.getItem(cacheKey);

    if (cached) {
      try {
        const { data, timestamp } = JSON.parse(cached);
        // Use cache if less than 5 minutes old
        if (Date.now() - timestamp < 5 * 60 * 1000) {
          return data;
        }
      } catch (e) {
        // Ignore cache errors
      }
    }

    try {
      const result = await apiHelper.get<{ isActive: boolean; partner?: ReferralPartner }>(
        '/referral-partners/check-status'
      );

      // Cache the result
      localStorage.setItem(
        cacheKey,
        JSON.stringify({
          data: result,
          timestamp: Date.now(),
        })
      );

      return result;
    } catch (error: any) {
      // For any error (404, 403, etc), assume user is not a partner
      const result = { isActive: false };

      // Only cache if it's a 404 (not found) error
      // Don't cache other errors like 403 or 500
      if (error.response?.status === 404) {
        localStorage.setItem(
          cacheKey,
          JSON.stringify({
            data: result,
            timestamp: Date.now(),
          })
        );
      }

      // Don't throw error, just return inactive status
      return result;
    }
  }
}

export const referralApi = new ReferralApiService();
