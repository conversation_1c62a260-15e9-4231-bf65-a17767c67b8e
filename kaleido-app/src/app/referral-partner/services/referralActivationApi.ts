import apiHelper from '@/lib/apiHelper';

export interface ReferralPartner {
  id: string;
  referralCode: string;
  clientId: string;
  partnerName: string;
  contactEmail: string;
  contactPhone?: string;
  totalEarnings: number;
  pendingEarnings: number;
  paidEarnings: number;
  isActive: boolean;
  dashboardMetrics?: {
    totalReferrals: number;
    successfulPlacements: number;
    conversionRate: number;
    averageBounty: number;
  };
}

export const referralActivationApi = {
  /**
   * Activate referral program for job seeker
   */
  activateForJobSeeker: async (): Promise<ReferralPartner> => {
    const response = await apiHelper.post('/referral/activation/job-seeker', {});
    return response.data;
  },

  /**
   * Activate referral program for company
   */
  activateForCompany: async (): Promise<ReferralPartner> => {
    const response = await apiHelper.post('/referral/activation/company', {});
    return response.data;
  },

  /**
   * Deactivate referral program
   */
  deactivate: async (): Promise<void> => {
    await apiHelper.delete('/referral/activation/deactivate');
  },

  /**
   * Check if user has referral partner status
   */
  checkStatus: async (): Promise<{ isActive: boolean; partner?: ReferralPartner }> => {
    try {
      const response = await apiHelper.get('/referral/partner/profile');
      return { isActive: true, partner: response.data };
    } catch (error: any) {
      if (error.response?.status === 404) {
        return { isActive: false };
      }
      throw error;
    }
  },
};
