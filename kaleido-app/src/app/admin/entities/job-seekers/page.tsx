'use client';

import React, { useEffect, useState } from 'react';

import { motion } from 'framer-motion';
import { Calendar, Eye, Mail, MapPin, Phone, User } from 'lucide-react';
import { useRouter } from 'next/navigation';

import EntityDetailDrawer from '@/components/admin/EntityDetailDrawer';
import ColorfulSmokeyOrbLoader from '@/components/Layouts/ColourfulLoader';
import GenericTable from '@/components/Layouts/GenericTable';
import AppLayout from '@/components/steps/layout/BaseLayout';
import { Button } from '@/components/ui/button';
import apiHelper from '@/lib/apiHelper';
import { UserRole } from '@/shared/types';

interface JobSeeker {
  id: string;
  userId: string;
  clientId?: string;
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  location?: string;
  myProfileImage?: string;
  summary?: string;
  skills: string[];
  experience?: any[];
  resumeUrl?: string;
  linkedinUrl?: string;
  githubUrl?: string;
  role: string;
  education?: any[];
  certifications?: any[];
  languages?: string[];
  myValues?: string[];
  portfolioUrl?: string;
  videoIntroUrl?: string;
  createdAt: string;
  updatedAt: string;
  isApproved?: boolean;
  declineReason?: string;
  hasCompletedOnboarding?: boolean;
  isImportedFromLinkedIn?: boolean;
}

export default function JobSeekersPage() {
  const router = useRouter();
  const [jobSeekers, setJobSeekers] = useState<JobSeeker[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedJobSeeker, setSelectedJobSeeker] = useState<JobSeeker | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalItems, setTotalItems] = useState(0);
  const itemsPerPage = 10;

  const fetchJobSeekers = async (page = 1) => {
    setIsLoading(true);
    try {
      const response = await apiHelper.get(
        `/job-seekers/admin/all?page=${page}&limit=${itemsPerPage}`
      );
      setJobSeekers(response.items);
      setTotalPages(response.pagination.totalPages);
      setTotalItems(response.pagination.totalItems);
      setCurrentPage(page);
    } catch (error) {
      console.error('Error fetching job seekers:', error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchJobSeekers();
  }, []);

  const handlePageChange = (page: number) => {
    fetchJobSeekers(page);
  };

  const handleApprove = async (id: string) => {
    try {
      await apiHelper.put(`/job-seekers/${id}/approve`, {});
      // Refresh the data
      fetchJobSeekers(currentPage);
    } catch (error) {
      console.error('Error approving job seeker:', error);
    }
  };

  const handleDecline = async (id: string, reason: string) => {
    try {
      await apiHelper.put(`/job-seekers/${id}/decline`, { reason });
      // Refresh the data
      fetchJobSeekers(currentPage);
    } catch (error) {
      console.error('Error declining job seeker:', error);
    }
  };

  const columns = [
    {
      key: 'fullName',
      label: 'Name',
      render: (_: any, row: JobSeeker) => `${row.firstName} ${row.lastName}`,
    },
    { key: 'email', label: 'Email', icon: Mail },
    { key: 'phone', label: 'Phone', icon: Phone },
    { key: 'location', label: 'Location', icon: MapPin },
    {
      key: 'skills',
      label: 'Skills',
      render: (value: string[]) =>
        value && value.length > 0
          ? value.slice(0, 3).join(', ') + (value.length > 3 ? '...' : '')
          : 'No skills listed',
    },
    {
      key: 'hasCompletedOnboarding',
      label: 'Onboarding',
      render: (value: boolean) =>
        value ? (
          <span className="text-[var(--success-color, #22c55e)]">Completed</span>
        ) : (
          <span className="text-[var(--warning-color, #eab308)]">Incomplete</span>
        ),
    },
    {
      key: 'createdAt',
      label: 'Created At',
      icon: Calendar,
      render: (value: string) => new Date(value).toLocaleDateString(),
    },
    {
      key: 'isApproved',
      label: 'Status',
      render: (value: boolean | undefined) => {
        if (value === true)
          return <span className="text-[var(--success-color, #22c55e)]">Approved</span>;
        if (value === false)
          return <span className="text-[var(--error-color, #ef4444)]">Declined</span>;
        return <span className="text-[var(--warning-color, #eab308)]">Pending</span>;
      },
    },
  ];

  return (
    <AppLayout userRole={UserRole.ADMIN}>
      <div className="max-w-7xl mx-auto p-6">
        <div className="mb-6 flex justify-between items-center">
          <div>
            <div className="flex items-center gap-2 mb-2">
              <div className="p-2 bg-pink-700/10 rounded-full">
                <User className="w-5 h-5 text-pink-700" />
              </div>
              <h1 className="text-2xl font-bold text-white/90">
                Job Seekers{' '}
                <span className="text-sm bg-pink-700/20 text-pink-700 rounded-full px-2 py-0.5 ml-2">
                  {totalItems}
                </span>
              </h1>
            </div>
            <p className="text-white/70">
              View and manage job seeker registrations. Approve or decline new registrations.
            </p>
          </div>
          <Button
            variant="outline"
            className="border-white/10"
            onClick={() => router.push('/admin/entities')}
          >
            Back to Entities
          </Button>
        </div>

        {isLoading ? (
          <ColorfulSmokeyOrbLoader text="Loading job seekers..." useModalBg={false} />
        ) : (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <GenericTable
              data={jobSeekers}
              columns={columns}
              onRowClick={jobSeeker => setSelectedJobSeeker(jobSeeker as JobSeeker)}
              customActions={jobSeeker => (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={e => {
                    e.stopPropagation();
                    setSelectedJobSeeker(jobSeeker as JobSeeker);
                  }}
                  className="hover:bg-white/20 border-white/10 border hover:text-white/80"
                >
                  <Eye className="w-4 h-4 mr-2" />
                  Details
                </Button>
              )}
              hideDelete
              hideDetailsUrl
              showPagination={true}
              currentPage={currentPage}
              totalItems={totalItems}
              paginationData={{
                currentPage,
                totalPages,
                totalItems,
                itemsPerPage,
              }}
              onPageChange={handlePageChange}
            />
          </motion.div>
        )}

        {selectedJobSeeker && (
          <EntityDetailDrawer
            isOpen={!!selectedJobSeeker}
            onClose={() => setSelectedJobSeeker(null)}
            entity={selectedJobSeeker}
            entityType="job-seeker"
            onApprove={handleApprove}
            onDecline={handleDecline}
          />
        )}
      </div>
    </AppLayout>
  );
}
