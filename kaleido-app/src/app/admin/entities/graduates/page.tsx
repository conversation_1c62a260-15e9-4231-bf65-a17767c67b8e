'use client';

import React, { useEffect, useState } from 'react';

import { motion } from 'framer-motion';
import { Eye, GraduationCap } from 'lucide-react';
import { useRouter } from 'next/navigation';

import EntityDetailDrawer from '@/components/admin/EntityDetailDrawer';
import ColorfulSmokeyOrbLoader from '@/components/Layouts/ColourfulLoader';
import GenericTable from '@/components/Layouts/GenericTable';
import AppLayout from '@/components/steps/layout/AppLayout';
import { Button } from '@/components/ui/button';
import apiHelper from '@/lib/apiHelper';

interface Graduate {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  university?: string;
  degreeProgram?: string;
  createdAt: string;
  isApproved?: boolean;
  declineReason?: string;
}

export default function GraduatesPage() {
  const router = useRouter();
  const [graduates, setGraduates] = useState<Graduate[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedGraduate, setSelectedGraduate] = useState<Graduate | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalItems, setTotalItems] = useState(0);
  const itemsPerPage = 10;

  const fetchGraduates = async (page = 1) => {
    setIsLoading(true);
    try {
      const response = await apiHelper.get(
        `/graduates/admin/all?page=${page}&limit=${itemsPerPage}`
      );
      setGraduates(response.items);
      setTotalPages(response.pagination.totalPages);
      setTotalItems(response.pagination.totalItems);
      setCurrentPage(page);
    } catch (error) {
      console.error('Error fetching graduates:', error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchGraduates();
  }, []);

  const handlePageChange = (page: number) => {
    fetchGraduates(page);
  };

  const handleApprove = async (id: string) => {
    try {
      await apiHelper.put(`/graduates/${id}/approve`, {});
      // Refresh the data
      fetchGraduates(currentPage);
    } catch (error) {
      console.error('Error approving graduate:', error);
    }
  };

  const handleDecline = async (id: string, reason: string) => {
    try {
      await apiHelper.put(`/graduates/${id}/decline`, { reason });
      // Refresh the data
      fetchGraduates(currentPage);
    } catch (error) {
      console.error('Error declining graduate:', error);
    }
  };

  const columns = [
    {
      key: 'fullName',
      label: 'Name',
      render: (_: any, row: Graduate) => `${row.firstName} ${row.lastName}`,
    },
    { key: 'email', label: 'Email' },
    { key: 'university', label: 'University' },
    { key: 'degreeProgram', label: 'Degree Program' },
    {
      key: 'createdAt',
      label: 'Created At',
      render: (value: string) => new Date(value).toLocaleDateString(),
    },
    {
      key: 'isApproved',
      label: 'Status',
      render: (value: boolean | undefined) => {
        if (value === true)
          return <span className="text-[var(--success-color, #22c55e)]">Approved</span>;
        if (value === false)
          return <span className="text-[var(--error-color, #ef4444)]">Declined</span>;
        return <span className="text-[var(--warning-color, #eab308)]">Pending</span>;
      },
    },
  ];

  return (
    <AppLayout>
      <div className="max-w-7xl mx-auto p-6">
        <div className="mb-6 flex justify-between items-center">
          <div>
            <div className="flex items-center gap-2 mb-2">
              <div className="p-2 bg-pink-500/10 rounded-full">
                <GraduationCap className="w-5 h-5 text-pink-400" />
              </div>
              <h1 className="text-2xl font-bold text-white/90">Graduates</h1>
            </div>
            <p className="text-white/70">
              View and manage graduate registrations. Approve or decline new registrations.
            </p>
          </div>
          <Button
            variant="outline"
            className="border-white/10"
            onClick={() => router.push('/admin/entities')}
          >
            Back to Entities
          </Button>
        </div>

        {isLoading ? (
          <ColorfulSmokeyOrbLoader text="Loading graduates..." useModalBg={false} />
        ) : (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <GenericTable
              data={graduates}
              columns={columns}
              onRowClick={graduate => setSelectedGraduate(graduate as Graduate)}
              customActions={graduate => (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={e => {
                    e.stopPropagation();
                    setSelectedGraduate(graduate as Graduate);
                  }}
                  className="hover:bg-white/20 border-white/10 border hover:text-white/80"
                >
                  <Eye className="w-4 h-4 mr-2" />
                  Details
                </Button>
              )}
              hideDelete
              hideDetailsUrl
              showPagination={true}
              currentPage={currentPage}
              totalItems={totalItems}
              paginationData={{
                currentPage,
                totalPages,
                totalItems,
                itemsPerPage,
              }}
              onPageChange={handlePageChange}
            />
          </motion.div>
        )}

        {selectedGraduate && (
          <EntityDetailDrawer
            isOpen={!!selectedGraduate}
            onClose={() => setSelectedGraduate(null)}
            entity={selectedGraduate}
            entityType="graduate"
            onApprove={handleApprove}
            onDecline={handleDecline}
          />
        )}
      </div>
    </AppLayout>
  );
}
