'use client';

import React, { useEffect, useState } from 'react';

import { motion } from 'framer-motion';
import { Building2, Eye } from 'lucide-react';
import { useRouter } from 'next/navigation';

import EntityDetailDrawer from '@/components/admin/EntityDetailDrawer';
import ColorfulSmokeyOrbLoader from '@/components/Layouts/ColourfulLoader';
import GenericTable from '@/components/Layouts/GenericTable';
import AppLayout from '@/components/steps/layout/AppLayout';
import { Button } from '@/components/ui/button';
import apiHelper from '@/lib/apiHelper';

interface Employer {
  id: string;
  companyName: string;
  industry: string;
  location: string;
  contactEmail: string;
  createdAt: string;
  isApproved?: boolean;
  declineReason?: string;
}

export default function EmployersPage() {
  const router = useRouter();
  const [employers, setEmployers] = useState<Employer[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedEmployer, setSelectedEmployer] = useState<Employer | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalItems, setTotalItems] = useState(0);
  const itemsPerPage = 10;

  const fetchEmployers = async (page = 1) => {
    setIsLoading(true);
    try {
      const response = await apiHelper.get('/companies');
      // Since the backend returns an array directly, we need to handle pagination on the frontend
      const allEmployers = response as Employer[];
      const startIndex = (page - 1) * itemsPerPage;
      const endIndex = startIndex + itemsPerPage;
      const paginatedEmployers = allEmployers.slice(startIndex, endIndex);

      setEmployers(paginatedEmployers);
      setTotalPages(Math.ceil(allEmployers.length / itemsPerPage));
      setTotalItems(allEmployers.length);
      setCurrentPage(page);
    } catch (error) {
      console.error('Error fetching employers:', error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchEmployers();
  }, []);

  const handlePageChange = (page: number) => {
    fetchEmployers(page);
  };

  const handleApprove = async (id: string) => {
    try {
      await apiHelper.put(`/companies/${id}/approve`, {});
      // Refresh the data
      fetchEmployers(currentPage);
    } catch (error) {
      console.error('Error approving employer:', error);
    }
  };

  const handleDecline = async (id: string, reason: string) => {
    try {
      await apiHelper.put(`/companies/${id}/decline`, { reason });
      // Refresh the data
      fetchEmployers(currentPage);
    } catch (error) {
      console.error('Error declining employer:', error);
    }
  };

  const columns = [
    { key: 'companyName', label: 'Company Name' },
    { key: 'industry', label: 'Industry' },
    { key: 'location', label: 'Location' },
    { key: 'contactEmail', label: 'Contact Email' },
    {
      key: 'createdAt',
      label: 'Created At',
      render: (value: string) => new Date(value).toLocaleDateString(),
    },
    {
      key: 'isApproved',
      label: 'Status',
      render: (value: boolean | undefined) => {
        if (value === true)
          return <span className="text-[var(--success-color, #22c55e)]">Approved</span>;
        if (value === false)
          return <span className="text-[var(--error-color, #ef4444)]">Declined</span>;
        return <span className="text-[var(--warning-color, #eab308)]">Pending</span>;
      },
    },
  ];

  return (
    <AppLayout>
      <div className="max-w-7xl mx-auto p-6">
        <div className="mb-6 flex justify-between items-center">
          <div>
            <div className="flex items-center gap-2 mb-2">
              <div className="p-2 bg-blue-500/10 rounded-full">
                <Building2 className="w-5 h-5 text-blue-400" />
              </div>
              <h1 className="text-2xl font-bold text-white/90">Employers</h1>
            </div>
            <p className="text-white/70">
              View and manage employer registrations. Approve or decline new registrations.
            </p>
          </div>
          <Button
            variant="outline"
            className="border-white/10"
            onClick={() => router.push('/admin/entities')}
          >
            Back to Entities
          </Button>
        </div>

        {isLoading ? (
          <ColorfulSmokeyOrbLoader text="Loading employers..." useModalBg={false} />
        ) : (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <GenericTable
              data={employers}
              columns={columns}
              onRowClick={employer => setSelectedEmployer(employer as Employer)}
              customActions={employer => (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={e => {
                    e.stopPropagation();
                    setSelectedEmployer(employer as Employer);
                  }}
                  className="hover:bg-white/20 border-white/10 border hover:text-white/80"
                >
                  <Eye className="w-4 h-4 mr-2" />
                  Details
                </Button>
              )}
              hideDelete
              hideDetailsUrl
              showPagination={true}
              currentPage={currentPage}
              totalItems={totalItems}
              paginationData={{
                currentPage,
                totalPages,
                totalItems,
                itemsPerPage,
              }}
              onPageChange={handlePageChange}
            />
          </motion.div>
        )}

        {selectedEmployer && (
          <EntityDetailDrawer
            isOpen={!!selectedEmployer}
            onClose={() => setSelectedEmployer(null)}
            entity={selectedEmployer}
            entityType="employer"
            onApprove={handleApprove}
            onDecline={handleDecline}
          />
        )}
      </div>
    </AppLayout>
  );
}
