'use client';

import React, { useEffect, useState } from 'react';
import {
  ChevronRight,
  Eye,
  EyeOff,
  FootprintsIcon,
  Globe,
  Hexagon,
  ImageIcon,
  Layers,
  Palette,
  Send,
  Sparkles,
  Check,
  AlertCircle,
  Info,
} from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import StyledInput from '@/components/common/styledInputs/StyledInput';
import ColorPicker from '@/components/company/ColorPicker';
import ImageSelector from '@/components/company/ImageSelector';
import MediaUpload from '@/components/company/MediaUpload';
import IconBasedLayoutSettings from '@/components/company/IconBasedLayoutSettings';
import SaveButton from '@/components/company/SaveButton';
import SettingsCard from '@/components/company/SettingsCard';
import { showToast } from '@/components/Toaster';
import apiHelper from '@/lib/apiHelper';
import { SaveChangesSlider } from '@/components/common/SaveChangesSlider';

// Helper function to convert company name to slug format
function slugify(text: string) {
  return text
    .toString()
    .toLowerCase()
    .replace(/\s+/g, '-')
    .replace(/[^\w-]+/g, '')
    .replace(/--+/g, '-')
    .replace(/^-+/, '')
    .replace(/-+$/, '');
}

export type ProfileStyleSettings = {
  layoutPreference: string;
  primaryColor: string;
  secondaryColor: string;
  accentColor: string;
  heroImage: string;
  footerImage: string;
  featuredImages: string[];
  customCss: string;
  logo?: string;
  companyName?: string;
  industry?: string;
};

type PublicProfileSettingsProps = {
  initialSettings: ProfileStyleSettings;
  onChange: (field: string, value: string | string[]) => void;
  companyId: string;
  clientId: string;
  handleSave: () => Promise<void>;
  handleCancel: () => void;
  isEditing: boolean;
};

interface SettingsSectionProps {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  children: React.ReactNode;
  isExpanded: boolean;
  onToggle: () => void;
  status?: 'complete' | 'incomplete' | 'warning';
  headerGradient?: React.ReactNode;
  bgColor?: string;
}

const SettingsSection: React.FC<SettingsSectionProps> = ({
  id,
  title,
  description,
  icon,
  children,
  isExpanded,
  onToggle,
  status = 'incomplete',
  headerGradient,
  bgColor = '',
}) => {
  const statusIcons = {
    complete: <Check className="w-3 h-3 text-green-400" />,
    incomplete: <AlertCircle className="w-3 h-3 text-yellow-400" />,
    warning: <AlertCircle className="w-3 h-3 text-orange-400" />,
  };

  const statusColors = {
    complete: 'border-green-400/5',
    incomplete: 'border-gray-300/5',
    warning: 'border-orange-400/5',
  };

  return (
    <motion.div
      layout
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.2 }}
      className={`rounded-xl bg-black/5 backdrop-blur-sm border ${statusColors[status]} shadow-sm shadow-black/5 transition-all duration-300 overflow-hidden hover:bg-black/10 hover:shadow-md hover:shadow-black/10`}
    >
      {headerGradient && isExpanded && <div className="h-1 w-full">{headerGradient}</div>}

      <div className="p-5 cursor-pointer" onClick={onToggle}>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4 flex-1">
            <div
              className={`p-3 rounded-xl transition-all duration-300 ${bgColor} backdrop-blur-sm`}
            >
              {icon}
            </div>
            <div className="flex-1">
              <h3 className="text-sm font-semibold flex items-center gap-2 text-white">
                {title}
                {statusIcons[status]}
              </h3>
              <p className="text-xs text-white/60 mt-1">{description}</p>
            </div>
          </div>
          <motion.div
            animate={{ rotate: isExpanded ? 90 : 0 }}
            transition={{ duration: 0.3, ease: 'easeInOut' }}
            className="p-1"
          >
            <ChevronRight className="w-4 h-4 text-white/60" />
          </motion.div>
        </div>
      </div>

      <AnimatePresence>
        {isExpanded && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.3, ease: 'easeInOut' }}
          >
            <div className="px-5 pb-5 pt-0">
              <div className="border-t border-white/5 pt-4">{children}</div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
};

const PublicProfileSettingsV2: React.FC<PublicProfileSettingsProps> = ({
  initialSettings,
  onChange,
  companyId,
  clientId,
  handleSave,
  handleCancel,
  isEditing,
}) => {
  const [previewUrl, setPreviewUrl] = useState('');
  const [isPublishing, setIsPublishing] = useState(false);
  const [isPublished, setIsPublished] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [expandedSections, setExpandedSections] = useState<string[]>(['visibility']);
  const [sectionStatuses, setSectionStatuses] = useState<
    Record<string, 'complete' | 'incomplete' | 'warning'>
  >({
    visibility: 'incomplete',
    layout: 'incomplete',
    colors: 'incomplete',
    media: 'incomplete',
    hero: 'incomplete',
    footer: 'incomplete',
    advanced: 'incomplete',
  });
  const [hasChanges, setHasChanges] = useState(false);
  const [currentSettings, setCurrentSettings] = useState<ProfileStyleSettings>(initialSettings);

  useEffect(() => {
    if (initialSettings.companyName) {
      setPreviewUrl(`/company-profile/${slugify(initialSettings.companyName)}`);
    }
    updateSectionStatuses();
    setCurrentSettings(initialSettings);
  }, [initialSettings]);

  // Check if settings have changed
  useEffect(() => {
    // Compare current isEditing with actual changes
    setHasChanges(isEditing);
  }, [isEditing]);

  const updateSectionStatuses = () => {
    const newStatuses: Record<string, 'complete' | 'incomplete' | 'warning'> = {
      visibility: isPublished ? 'complete' : 'incomplete',
      layout: currentSettings.layoutPreference !== 'default' ? 'complete' : 'incomplete',
      colors: currentSettings.primaryColor !== '#6366f1' ? 'complete' : 'incomplete',
      media: currentSettings.logo ? 'complete' : 'warning',
      hero: currentSettings.heroImage ? 'complete' : 'incomplete',
      footer: currentSettings.footerImage ? 'complete' : 'incomplete',
      advanced: currentSettings.customCss ? 'complete' : 'incomplete',
    };
    setSectionStatuses(newStatuses);
  };

  const toggleSection = (sectionId: string) => {
    setExpandedSections(prev =>
      prev.includes(sectionId) ? prev.filter(id => id !== sectionId) : [...prev, sectionId]
    );
  };

  const handlePublishProfile = async () => {
    try {
      setIsPublishing(true);
      await apiHelper.post(`/companies/${companyId}/publish`, {
        isPublished: true,
      });
      setIsPublished(true);
      showToast({
        message: 'Company profile published successfully',
        isSuccess: true,
      });
      updateSectionStatuses();
    } catch (error) {
      console.error('Error publishing company profile:', error);
      showToast({
        message: 'Failed to publish company profile',
        isSuccess: false,
      });
    } finally {
      setIsPublishing(false);
    }
  };

  const renderColorGradient = () => (
    <div
      className="h-full w-full"
      style={{
        background: `linear-gradient(to right, ${currentSettings.primaryColor}, ${currentSettings.secondaryColor}, ${currentSettings.accentColor})`,
      }}
    />
  );

  const sectionColors = {
    visibility: 'bg-blue-500/20 border border-blue-500/10',
    layout: 'bg-indigo-500/20 border border-indigo-500/10',
    colors: 'bg-purple-500/20 border border-purple-500/10',
    media: 'bg-pink-500/20 border border-pink-500/10',
    hero: 'bg-emerald-500/20 border border-emerald-500/10',
    footer: 'bg-cyan-500/20 border border-cyan-500/10',
    advanced: 'bg-amber-500/20 border border-amber-500/10',
  };

  const iconColors = {
    visibility: 'text-blue-400',
    layout: 'text-indigo-400',
    colors: 'text-purple-400',
    media: 'text-pink-400',
    hero: 'text-emerald-400',
    footer: 'text-cyan-400',
    advanced: 'text-amber-400',
  };

  const sections = [
    {
      id: 'visibility',
      title: 'Visibility Control',
      description: "Control who can see your company profile and when it's live",
      icon: isPublished ? (
        <Eye className={`w-5 h-5 ${iconColors.visibility}`} />
      ) : (
        <EyeOff className={`w-5 h-5 ${iconColors.visibility}`} />
      ),
      bgColor: sectionColors.visibility,
      content: (
        <div className="space-y-4">
          <div className="flex items-center justify-between p-4 rounded-xl bg-black/5 backdrop-blur-sm border border-white/5">
            <div className="flex items-center gap-4">
              <div
                className={`p-3 rounded-xl backdrop-blur-sm ${isPublished ? 'bg-green-500/20 border border-green-500/10' : 'bg-yellow-500/20 border border-yellow-500/10'}`}
              >
                {isPublished ? (
                  <Globe className="w-5 h-5 text-green-400" />
                ) : (
                  <EyeOff className="w-5 h-5 text-yellow-400" />
                )}
              </div>
              <div>
                <p className="font-semibold text-white">Profile Status</p>
                <p className="text-sm text-white/60">
                  {isPublished
                    ? 'Your profile is live and visible to the public'
                    : 'Your profile is currently private'}
                </p>
              </div>
            </div>
            <button
              type="button"
              onClick={handlePublishProfile}
              disabled={isPublishing}
              className={`px-4 py-2 rounded-xl font-medium text-sm transition-all duration-300 backdrop-blur-sm ${
                isPublished
                  ? 'bg-red-500/20 text-red-400 hover:bg-red-500/30 border border-red-500/10 hover:border-red-500/20'
                  : 'bg-gradient-to-r from-purple-500/20 to-pink-500/20 text-white hover:from-purple-500/30 hover:to-pink-500/30 border border-purple-500/10 hover:border-purple-500/20'
              }`}
            >
              {isPublishing ? (
                <span className="flex items-center gap-2">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current" />
                  Publishing...
                </span>
              ) : isPublished ? (
                'Unpublish'
              ) : (
                'Publish Profile'
              )}
            </button>
          </div>

          {previewUrl && (
            <a
              href={previewUrl}
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center gap-2 px-4 py-2 rounded-xl bg-black/5 hover:bg-black/10 transition-all duration-300 backdrop-blur-sm border border-white/5 hover:border-white/10 text-white text-sm font-medium"
            >
              <Globe className="w-4 h-4" />
              Preview Public Profile
            </a>
          )}
        </div>
      ),
    },
    {
      id: 'layout',
      title: 'Layout Selection',
      description: 'Choose a landing page style that best showcases your brand',
      icon: <Layers className={`w-5 h-5 ${iconColors.layout}`} />,
      bgColor: sectionColors.layout,
      content: (
        <div className="space-y-4">
          <div className="flex items-center gap-2">
            <h4 className="text-sm font-medium">Choose Your Layout</h4>
            <div className="group relative">
              <Info className="w-4 h-4 text-muted-foreground cursor-help" />
              <div className="absolute bottom-full left-1/2 -translate-x-1/2 mb-2 px-3 py-2 bg-black/80 backdrop-blur-md text-white text-xs rounded-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 whitespace-nowrap shadow-lg z-50 border border-white/10">
                Choose a landing page style that best showcases your brand
                <div className="absolute top-full left-1/2 -translate-x-1/2 -mt-1 w-2 h-2 bg-black/80 rotate-45 border-r border-b border-white/10" />
              </div>
            </div>
          </div>

          <IconBasedLayoutSettings
            currentLayout={currentSettings.layoutPreference || 'modern'}
            onLayoutChange={layout => {
              setCurrentSettings({ ...currentSettings, layoutPreference: layout });
              onChange('layoutPreference', layout);
              updateSectionStatuses();
            }}
          />

          {previewUrl && (
            <div className="pt-3 border-t border-white/5">
              <p className="text-xs text-white/60 mb-3">Preview your layout:</p>
              <a
                href={previewUrl}
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center gap-2 px-4 py-2 text-xs rounded-xl bg-black/5 hover:bg-black/10 transition-all duration-300 backdrop-blur-sm border border-white/5 hover:border-white/10 text-white"
              >
                <Globe className="w-3.5 h-3.5" />
                Preview in New Tab
              </a>
            </div>
          )}
        </div>
      ),
    },
    {
      id: 'colors',
      title: 'Brand Colors',
      description: 'Define the color palette that reflects your brand identity',
      icon: <Palette className={`w-5 h-5 ${iconColors.colors}`} />,
      bgColor: sectionColors.colors,
      headerGradient: renderColorGradient(),
      content: (
        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {[
              { id: 'primaryColor', label: 'Primary Color', description: 'Main brand color' },
              { id: 'secondaryColor', label: 'Secondary Color', description: 'Supporting color' },
              { id: 'accentColor', label: 'Accent Color', description: 'Highlight color' },
            ].map(color => (
              <div
                key={color.id}
                className="space-y-3 p-4 rounded-xl bg-black/5 backdrop-blur-sm border border-white/5"
              >
                <div>
                  <label className="text-sm font-semibold text-white">{color.label}</label>
                  <p className="text-xs text-white/60 mt-1">{color.description}</p>
                </div>
                <ColorPicker
                  id={color.id}
                  label=""
                  color={currentSettings[color.id as keyof typeof currentSettings] as string}
                  onChange={value => {
                    onChange(color.id, value);
                    updateSectionStatuses();
                  }}
                />
              </div>
            ))}
          </div>

          <div className="p-4 rounded-xl bg-black/5 backdrop-blur-sm border border-white/5">
            <p className="text-sm font-semibold mb-3 text-white">Color Preview</p>
            <div
              className="h-16 rounded-xl shadow-inner"
              style={{
                background: `linear-gradient(135deg, ${currentSettings.primaryColor} 0%, ${currentSettings.secondaryColor} 50%, ${currentSettings.accentColor} 100%)`,
              }}
            />
          </div>
        </div>
      ),
    },
    {
      id: 'media',
      title: 'Media Assets',
      description: 'Upload your company logo and other brand assets',
      icon: <ImageIcon className={`w-5 h-5 ${iconColors.media}`} />,
      bgColor: sectionColors.media,
      content: (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <MediaUpload
            label="Company Logo"
            url={currentSettings.logo || ''}
            placeholder="Enter logo URL"
            emptyIcon={<Hexagon className="h-12 w-12 text-muted-foreground" />}
            emptyText="Upload your company logo"
            altText={currentSettings.companyName || 'Company Logo'}
            objectFit="object-contain"
            onChange={value => {
              setCurrentSettings({ ...currentSettings, logo: value });
              onChange('logo', value);
              updateSectionStatuses();
            }}
          />

          <MediaUpload
            label="Hero Image"
            url={currentSettings.heroImage || ''}
            placeholder="Enter hero image URL"
            emptyIcon={<ImageIcon className="h-12 w-12 text-muted-foreground" />}
            emptyText="Upload a hero image"
            altText="Hero"
            objectFit="object-cover"
            onChange={value => {
              setCurrentSettings({ ...currentSettings, heroImage: value });
              onChange('heroImage', value);
              updateSectionStatuses();
            }}
          />
        </div>
      ),
    },
    {
      id: 'hero',
      title: 'Hero Background',
      description: 'Select a background image for your profile hero section',
      icon: <ImageIcon className={`w-5 h-5 ${iconColors.hero}`} />,
      bgColor: sectionColors.hero,
      content: (
        <ImageSelector
          label="Hero Background"
          currentImage={currentSettings.heroImage}
          onChange={value => {
            setCurrentSettings({ ...currentSettings, heroImage: value });
            onChange('heroImage', value);
            updateSectionStatuses();
          }}
          imageBasePath="/images/templates"
          imageCount={41}
          imagePrefix="bg"
          modalTitle="Select Hero Background Image"
          emptyText="Click to select a hero background image"
        />
      ),
    },
    {
      id: 'footer',
      title: 'Footer Image',
      description: 'Choose an image for your profile footer section',
      icon: <FootprintsIcon className={`w-5 h-5 ${iconColors.footer}`} />,
      bgColor: sectionColors.footer,
      content: (
        <ImageSelector
          label="Footer Image"
          currentImage={currentSettings.footerImage}
          onChange={value => {
            setCurrentSettings({ ...currentSettings, footerImage: value });
            onChange('footerImage', value);
            updateSectionStatuses();
          }}
          imageBasePath="/images/templates"
          imageCount={41}
          imagePrefix="bg"
          modalTitle="Select Footer Image"
          emptyText="Click to select a footer image"
        />
      ),
    },
    {
      id: 'advanced',
      title: 'Advanced Customization',
      description: 'Add custom CSS for fine-tuned control over your profile appearance',
      icon: <Sparkles className={`w-5 h-5 ${iconColors.advanced}`} />,
      bgColor: sectionColors.advanced,
      content: (
        <div className="space-y-4">
          <div className="rounded-xl overflow-hidden border border-white/10 bg-white/5 backdrop-blur-sm">
            <div className="flex items-center justify-between bg-black/20 px-4 py-2 border-b border-white/5">
              <span className="text-xs font-mono text-white/80">custom.css</span>
              <div className="flex gap-1.5">
                <span className="w-2 h-2 rounded-full bg-red-400" />
                <span className="w-2 h-2 rounded-full bg-yellow-400" />
                <span className="w-2 h-2 rounded-full bg-green-400" />
              </div>
            </div>
            <StyledInput
              multiline
              rows={8}
              placeholder="/* Enter your custom CSS here */
.company-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  padding: 2rem;
}"
              value={currentSettings.customCss}
              onChange={e => {
                setCurrentSettings({ ...currentSettings, customCss: e.target.value });
                onChange('customCss', e.target.value);
                updateSectionStatuses();
              }}
              className="font-mono text-sm border-0 rounded-none bg-transparent text-white placeholder:text-white/40"
            />
          </div>

          <div className="p-4 rounded-xl bg-amber-500/10 border border-amber-500/10 backdrop-blur-sm">
            <p className="text-xs text-amber-400">
              <strong>Note:</strong> CSS changes take effect after saving and may require a page
              refresh.
            </p>
          </div>
        </div>
      ),
    },
  ];

  return (
    <div className="space-y-4 pb-20">
      {sections.map(section => (
        <SettingsSection
          key={section.id}
          id={section.id}
          title={section.title}
          description={section.description}
          icon={section.icon}
          isExpanded={expandedSections.includes(section.id)}
          onToggle={() => toggleSection(section.id)}
          status={sectionStatuses[section.id as keyof typeof sectionStatuses]}
          headerGradient={section.headerGradient}
          bgColor={section.bgColor}
        >
          {section.content}
        </SettingsSection>
      ))}

      <SaveChangesSlider
        isVisible={hasChanges}
        onSave={async () => {
          setIsSaving(true);
          await handleSave();
          setIsSaving(false);
        }}
        onCancel={handleCancel}
        isSaving={isSaving}
        saveText="Save All Changes"
        cancelText="Discard"
      />
    </div>
  );
};

export default PublicProfileSettingsV2;
