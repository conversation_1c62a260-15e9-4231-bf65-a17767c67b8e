'use client';

import React, { useState } from 'react';
import { ModernProgress, LoadingState } from './modern-progress';

export const ProgressShowcase: React.FC = () => {
  const [progress1, setProgress1] = useState(0);
  const [progress2, setProgress2] = useState(0);
  const [progress3, setProgress3] = useState(0);
  const [progress4, setProgress4] = useState(0);

  // Simulate progress
  React.useEffect(() => {
    const timer1 = setInterval(() => {
      setProgress1(prev => {
        if (prev >= 100) return 0;
        return prev + Math.random() * 15;
      });
    }, 800);

    const timer2 = setInterval(() => {
      setProgress2(prev => {
        if (prev >= 100) return 0;
        return prev + Math.random() * 10;
      });
    }, 600);

    const timer3 = setInterval(() => {
      setProgress3(prev => {
        if (prev >= 100) return 0;
        return prev + Math.random() * 8;
      });
    }, 700);

    const timer4 = setInterval(() => {
      setProgress4(prev => {
        if (prev >= 100) return 0;
        return prev + Math.random() * 12;
      });
    }, 900);

    return () => {
      clearInterval(timer1);
      clearInterval(timer2);
      clearInterval(timer3);
      clearInterval(timer4);
    };
  }, []);

  return (
    <div className="p-8 space-y-8 bg-gray-50 dark:bg-gray-900 rounded-lg">
      <h2 className="text-2xl font-bold text-gray-800 dark:text-gray-200">
        Modern Progress Bar Showcase
      </h2>

      <div className="space-y-6">
        <div>
          <h3 className="text-lg font-semibold mb-3 text-gray-700 dark:text-gray-300">
            Gradient Animation
          </h3>
          <ModernProgress value={progress1} variant="gradient" />
        </div>

        <div>
          <h3 className="text-lg font-semibold mb-3 text-gray-700 dark:text-gray-300">
            Pulse Effect
          </h3>
          <ModernProgress value={progress2} variant="pulse" />
        </div>

        <div>
          <h3 className="text-lg font-semibold mb-3 text-gray-700 dark:text-gray-300">
            Wave Animation
          </h3>
          <ModernProgress value={progress3} variant="wave" />
        </div>

        <div>
          <h3 className="text-lg font-semibold mb-3 text-gray-700 dark:text-gray-300">
            Default Style
          </h3>
          <ModernProgress value={progress4} variant="default" />
        </div>

        <div className="pt-8 border-t border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-semibold mb-3 text-gray-700 dark:text-gray-300">
            Loading State with Messages
          </h3>
          <LoadingState progress={Math.min(progress1, 100)} variant="gradient" />
        </div>
      </div>
    </div>
  );
};
