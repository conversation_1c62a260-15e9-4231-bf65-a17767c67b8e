'use client';

import * as React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@/lib/utils';

interface ModernProgressProps extends React.HTMLAttributes<HTMLDivElement> {
  value: number;
  showPercentage?: boolean;
  indicatorClassName?: string;
  animated?: boolean;
  variant?: 'default' | 'gradient' | 'pulse' | 'wave';
}

const ModernProgress = React.forwardRef<HTMLDivElement, ModernProgressProps>(
  (
    {
      className,
      indicatorClassName,
      value = 0,
      showPercentage = true,
      animated = true,
      variant = 'gradient',
      ...props
    },
    ref
  ) => {
    const [displayValue, setDisplayValue] = React.useState(0);
    const [pulseKey, setPulseKey] = React.useState(0);

    // Smooth value animation with pulse on update
    React.useEffect(() => {
      if (animated) {
        // Increment pulse key to restart animations even at same value
        setPulseKey(prev => prev + 1);

        const timer = setTimeout(() => {
          setDisplayValue(value);
        }, 100);
        return () => clearTimeout(timer);
      } else {
        setDisplayValue(value);
      }
    }, [value, animated]);

    const getIndicatorClassName = () => {
      switch (variant) {
        case 'gradient':
          return cn(
            'bg-gradient-to-r from-purple-600 via-pink-600 to-purple-600',
            'bg-[length:200%_100%] animate-gradient-x',
            'shadow-lg shadow-purple-500/25'
          );
        case 'pulse':
          return cn(
            'bg-gradient-to-r from-blue-500 to-cyan-500',
            'animate-pulse-scale',
            'shadow-lg shadow-blue-500/30'
          );
        case 'wave':
          return cn(
            'bg-gradient-to-r from-emerald-500 via-teal-500 to-emerald-500',
            'bg-[length:200%_100%] animate-wave',
            'shadow-lg shadow-emerald-500/25'
          );
        default:
          return 'bg-purple-600 shadow-md shadow-purple-500/20';
      }
    };

    return (
      <div className={cn('relative', className)} ref={ref} {...props}>
        <div
          className={cn(
            'relative h-3 w-full overflow-hidden rounded-full',
            'bg-gray-200 dark:bg-gray-800',
            'shadow-inner'
          )}
        >
          <motion.div
            key={pulseKey}
            className={cn(
              'h-full rounded-full relative overflow-hidden',
              getIndicatorClassName(),
              indicatorClassName
            )}
            initial={{ width: displayValue > 0 ? `${displayValue}%` : 0, scale: 1 }}
            animate={{
              width: `${displayValue}%`,
              scale: [1, 1.02, 1],
            }}
            transition={{
              width: {
                duration: animated ? 0.8 : 0,
                ease: [0.32, 0.72, 0, 1],
              },
              scale: {
                duration: 0.4,
                times: [0, 0.5, 1],
                ease: 'easeInOut',
              },
            }}
          >
            {/* Animated shimmer effect */}
            <motion.div
              className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent"
              animate={{
                x: ['-100%', '100%'],
              }}
              transition={{
                duration: 1.5,
                repeat: Infinity,
                repeatDelay: 0.5,
                ease: 'linear',
              }}
            />

            {/* Pulse effect on the leading edge */}
            <motion.div
              className="absolute right-0 top-0 bottom-0 w-2 bg-white/40 blur-sm"
              animate={{
                opacity: [0.4, 1, 0.4],
              }}
              transition={{
                duration: 1.5,
                repeat: Infinity,
                ease: 'easeInOut',
              }}
            />
          </motion.div>
        </div>

        {/* Percentage display */}
        <AnimatePresence>
          {showPercentage && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              className="absolute -top-8 left-0 right-0 text-center"
            >
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                {Math.round(displayValue)}%
              </span>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Progress milestones */}
        <div className="absolute inset-0 flex items-center">
          {[25, 50, 75].map(milestone => (
            <div
              key={milestone}
              className={cn(
                'absolute w-0.5 h-full transition-opacity duration-300',
                displayValue >= milestone ? 'bg-white/20' : 'bg-gray-300 dark:bg-gray-700'
              )}
              style={{ left: `${milestone}%` }}
            />
          ))}
        </div>
      </div>
    );
  }
);

ModernProgress.displayName = 'ModernProgress';

// Loading states with different messages
interface LoadingStateProps {
  progress: number;
  message?: string;
  variant?: ModernProgressProps['variant'];
}

export const LoadingState: React.FC<LoadingStateProps> = ({
  progress,
  message,
  variant = 'gradient',
}) => {
  const messages = [
    { threshold: 0, text: 'Initializing...' },
    { threshold: 20, text: 'Gathering candidates...' },
    { threshold: 40, text: 'Analyzing profiles...' },
    { threshold: 60, text: 'Comparing qualifications...' },
    { threshold: 80, text: 'Generating insights...' },
    { threshold: 95, text: 'Finalizing results...' },
  ];

  const currentMessage =
    message || messages.filter(m => progress >= m.threshold).pop()?.text || 'Processing...';

  return (
    <div className="space-y-4">
      <ModernProgress value={progress} variant={variant} />
      <motion.p
        key={currentMessage}
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -10 }}
        className="text-sm text-gray-600 dark:text-gray-400 text-center"
      >
        {currentMessage}
      </motion.p>
    </div>
  );
};

export { ModernProgress };
