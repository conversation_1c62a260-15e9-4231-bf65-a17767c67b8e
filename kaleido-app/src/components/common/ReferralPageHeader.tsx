'use client';

import { AnimatePresence, motion } from 'framer-motion';
import { LucideIcon, TrendingUp, Wallet, DollarSign, Percent } from 'lucide-react';
import Image from 'next/image';
import { usePathname } from 'next/navigation';
import React, { useEffect, useState } from 'react';
import { SIDEBAR } from '@/constants/layout';

interface ReferralPageHeaderProps {
  title: string;
  description?: string;
  icon?: LucideIcon;
  className?: string;
  imageSrc?: string;
  imageHeight?: string;
  stats?: {
    totalEarnings: number;
    pendingEarnings: number;
    paidEarnings: number;
    conversionRate: number;
  };
}

// Map of page paths to gradient overlay configs
const pageGradientConfigs: Record<
  string,
  { from: string; via: string; to: string; opacity: number }
> = {
  '/referral-partner/earnings': {
    from: 'from-purple-900/80',
    via: 'via-pink-800/70',
    to: 'to-indigo-900/80',
    opacity: 0.9,
  },
  '/referral-partner/jobs': {
    from: 'from-indigo-900/80',
    via: 'via-purple-800/70',
    to: 'to-pink-900/80',
    opacity: 0.9,
  },
  default: {
    from: 'from-purple-900/80',
    via: 'via-purple-800/70',
    to: 'to-indigo-900/80',
    opacity: 0.9,
  },
};

const ReferralPageHeader: React.FC<ReferralPageHeaderProps> = ({
  title,
  description,
  icon: Icon,
  className = '',
  imageSrc = '/images/landing/open-jobs/open-jobs-5.webp',
  imageHeight = 'h-[320px]',
  stats,
}) => {
  const pathname = usePathname();
  const [currentImage, setCurrentImage] = useState(imageSrc);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [sidebarExpanded, setSidebarExpanded] = useState(false);

  // Get sidebar state from localStorage and listen for changes
  useEffect(() => {
    const checkSidebarState = () => {
      const savedState = localStorage.getItem('sidebarExpanded');
      if (savedState !== null) {
        setSidebarExpanded(JSON.parse(savedState));
      }
    };

    // Initial check
    checkSidebarState();

    // Listen for storage changes
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'sidebarExpanded') {
        checkSidebarState();
      }
    };

    window.addEventListener('storage', handleStorageChange);

    // Also check periodically for same-tab changes
    const interval = setInterval(checkSidebarState, 100);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      clearInterval(interval);
    };
  }, []);

  // Handle image transitions based on pathname
  useEffect(() => {
    let newImage = imageSrc;
    if (pathname === '/referral-partner/earnings') {
      newImage = '/images/insights/performance_analytics_turquoise.png';
    } else if (pathname === '/referral-partner/jobs') {
      newImage = '/images/insights/team_collaboration_revised.png';
    }

    if (newImage !== currentImage) {
      setIsTransitioning(true);
      setTimeout(() => {
        setCurrentImage(newImage);
        setIsTransitioning(false);
      }, 300);
    }
  }, [pathname, imageSrc, currentImage]);

  return (
    <div className={`fixed top-0 left-0 right-0 ${imageHeight} overflow-hidden ${className} z-0`}>
      {/* Background image with transitions */}
      <AnimatePresence mode="wait">
        <motion.div
          key={currentImage}
          initial={{ opacity: 0, scale: 1.05 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.95 }}
          transition={{ duration: 0.5, ease: 'easeInOut' }}
          className="absolute inset-0 z-0"
        >
          <Image
            src={currentImage}
            alt="Hero Background"
            fill
            priority
            quality={100}
            className="object-cover"
            sizes="100vw"
          />
        </motion.div>
      </AnimatePresence>

      {/* Very faint overlay */}
      <div className="absolute inset-0 z-5 bg-black/20" />

      {/* Additional decorative layers */}
      <div className="absolute inset-0 z-10">
        {/* Single bottom to top gradient overlay - dark purple to pink to transparent by midway */}
        <div className="absolute bottom-0 left-0 right-0 h-1/2 bg-gradient-to-t from-black/50 via-purple-900/50 via-70% to-transparent" />

        {/* Dynamic corner accents */}
        <motion.div
          className="absolute top-0 right-0 w-64 h-64 bg-gradient-radial from-pink-600/20 via-transparent to-transparent blur-3xl"
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.3, 0.5, 0.3],
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: 'easeInOut',
          }}
        />

        <motion.div
          className="absolute bottom-0 left-0 w-64 h-64 bg-gradient-radial from-purple-600/20 via-transparent to-transparent blur-3xl"
          animate={{
            scale: [1, 1.3, 1],
            opacity: [0.3, 0.6, 0.3],
          }}
          transition={{
            duration: 10,
            repeat: Infinity,
            ease: 'easeInOut',
            delay: 2,
          }}
        />
      </div>

      {/* Content overlay */}
      <div className="relative z-20 w-full h-full flex items-end">
        <div 
          className="w-full px-4 sm:px-6 lg:px-8 pb-8" 
          style={{ 
            paddingLeft: typeof window !== 'undefined' && window.innerWidth >= 1024 
              ? `calc(1rem + ${sidebarExpanded ? SIDEBAR.EXPANDED_WIDTH : SIDEBAR.COLLAPSED_WIDTH})`
              : '1rem'
          }}>
          <div className="flex items-end justify-between">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              className="flex-1"
            >
              <div className="flex items-center mb-2">
                {Icon && <Icon className="w-8 h-8 text-pink-400 mr-3" />}
                <h1 className="text-3xl sm:text-4xl font-bold text-white">{title}</h1>
              </div>
              {description && <p className="text-lg text-white/80 max-w-3xl">{description}</p>}
            </motion.div>

            {/* Stats Section */}
            {stats && (
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5, delay: 0.2 }}
                className="hidden lg:block ml-8"
              >
                <div
                  className="relative rounded-xl p-3 border border-white/10 shadow-2xl overflow-hidden"
                  style={{
                    background: 'rgba(255, 255, 255, 0.03)',
                    backdropFilter: 'blur(20px) saturate(180%)',
                    WebkitBackdropFilter: 'blur(20px) saturate(180%)',
                  }}
                >
                  {/* Glass effect layers */}
                  <div className="absolute inset-0 bg-gradient-to-br from-white/5 via-transparent to-white/3 pointer-events-none" />
                  <div className="absolute inset-0 bg-gradient-to-tr from-purple-500/3 via-transparent to-pink-500/3 pointer-events-none" />

                  <div className="relative flex items-center divide-x divide-white/10">
                    {/* Total Earnings */}
                    <motion.div
                      className="flex items-center gap-2 px-3 first:pl-0"
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.3 }}
                    >
                      <div
                        className="flex items-center justify-center w-8 h-8 rounded-lg bg-green-500/15 group-hover:bg-green-500/25 transition-colors"
                        style={{
                          backdropFilter: 'blur(8px)',
                          WebkitBackdropFilter: 'blur(8px)',
                        }}
                      >
                        <TrendingUp className="w-4 h-4 text-green-300" />
                      </div>
                      <div>
                        <p className="text-[10px] text-white/60 font-medium">Total</p>
                        <p className="text-sm font-bold text-white">
                          ${stats.totalEarnings.toLocaleString()}
                        </p>
                      </div>
                    </motion.div>

                    {/* Pending */}
                    <motion.div
                      className="flex items-center gap-2 px-3"
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.4 }}
                    >
                      <div
                        className="flex items-center justify-center w-8 h-8 rounded-lg bg-yellow-500/15 group-hover:bg-yellow-500/25 transition-colors"
                        style={{
                          backdropFilter: 'blur(8px)',
                          WebkitBackdropFilter: 'blur(8px)',
                        }}
                      >
                        <Wallet className="w-4 h-4 text-yellow-300" />
                      </div>
                      <div>
                        <p className="text-[10px] text-white/60 font-medium">Pending</p>
                        <p className="text-sm font-bold text-white">
                          ${stats.pendingEarnings.toLocaleString()}
                        </p>
                      </div>
                    </motion.div>

                    {/* Paid Out */}
                    <motion.div
                      className="flex items-center gap-2 px-3"
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.5 }}
                    >
                      <div
                        className="flex items-center justify-center w-8 h-8 rounded-lg bg-blue-500/15 group-hover:bg-blue-500/25 transition-colors"
                        style={{
                          backdropFilter: 'blur(8px)',
                          WebkitBackdropFilter: 'blur(8px)',
                        }}
                      >
                        <DollarSign className="w-4 h-4 text-blue-300" />
                      </div>
                      <div>
                        <p className="text-[10px] text-white/60 font-medium">Paid</p>
                        <p className="text-sm font-bold text-white">
                          ${stats.paidEarnings.toLocaleString()}
                        </p>
                      </div>
                    </motion.div>

                    {/* Conversion Rate */}
                    <motion.div
                      className="flex items-center gap-2 px-3 last:pr-0"
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.6 }}
                    >
                      <div
                        className="flex items-center justify-center w-8 h-8 rounded-lg bg-purple-500/15 group-hover:bg-purple-500/25 transition-colors"
                        style={{
                          backdropFilter: 'blur(8px)',
                          WebkitBackdropFilter: 'blur(8px)',
                        }}
                      >
                        <Percent className="w-4 h-4 text-purple-300" />
                      </div>
                      <div>
                        <p className="text-[10px] text-white/60 font-medium">Rate</p>
                        <p className="text-sm font-bold text-white">
                          {stats.conversionRate.toFixed(1)}%
                        </p>
                      </div>
                    </motion.div>
                  </div>
                </div>
              </motion.div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ReferralPageHeader;
