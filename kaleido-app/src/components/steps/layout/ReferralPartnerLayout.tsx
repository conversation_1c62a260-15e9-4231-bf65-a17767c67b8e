'use client';

import React, { useEffect, useState } from 'react';

import { useAuth0Token } from '@/hooks/useAuth0Token';
import { useOnboardingStore } from '@/stores/onboardingStore';
import { UserRole } from '@/types/roles';

import BaseLayout from './BaseLayout';

interface ReferralPartnerLayoutProps {
  children: React.ReactNode;
  isLoading?: boolean;
}

const ReferralPartnerLayout: React.FC<ReferralPartnerLayoutProps> = ({
  children,
  isLoading = false,
}) => {
  const { user, isLoading: userLoading } = useAuth0Token();
  const [isChecking, setIsChecking] = useState(true);
  const { setUserRole } = useOnboardingStore();

  // Set user role when user is available
  useEffect(() => {
    if (user && !userLoading) {
      setUserRole(UserRole.REFERRAL_PARTNER);
      setIsChecking(false); // User is loaded, stop checking
    }
  }, [user, userLoading, setUserRole]);

  // Show loading while checking
  if (isChecking || userLoading) {
    return (
      <BaseLayout userRole={UserRole.REFERRAL_PARTNER} isLoading={true}>
        {children}
      </BaseLayout>
    );
  }

  return (
    <BaseLayout userRole={UserRole.REFERRAL_PARTNER} isLoading={isLoading}>
      {children}
    </BaseLayout>
  );
};

export default ReferralPartnerLayout;
