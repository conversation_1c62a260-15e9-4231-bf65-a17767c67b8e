'use client';

import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import {
  DollarSign,
  Users,
  TrendingUp,
  Building2,
  Link,
  BarChart3,
  CheckCircle2,
  AlertCircle,
} from 'lucide-react';
import { useRouter } from 'next/navigation';
import { showToast } from '@/components/Toaster';
import { referralApi } from '@/app/referral-partner/services/referralApi';

interface PartnerProgramSettingsProps {
  companyName?: string;
}

export const PartnerProgramSettings: React.FC<PartnerProgramSettingsProps> = ({
  companyName = 'Your Company',
}) => {
  const router = useRouter();
  const [isActivating, setIsActivating] = useState(false);
  const [isChecking, setIsChecking] = useState(true);
  const [isActive, setIsActive] = useState(false);
  const [referralCode, setReferralCode] = useState<string | null>(null);
  const [partnerStats, setPartnerStats] = useState({
    totalReferrals: 0,
    successfulPlacements: 0,
    totalEarnings: 0,
  });

  useEffect(() => {
    checkReferralStatus();
  }, []);

  const checkReferralStatus = async () => {
    try {
      const { isActive: active, partner } = await referralApi.checkReferralStatus();
      setIsActive(active);
      if (partner) {
        setReferralCode(partner.referralCode);
        setPartnerStats({
          totalReferrals: partner.dashboardMetrics?.totalReferrals || 0,
          successfulPlacements: partner.dashboardMetrics?.successfulPlacements || 0,
          totalEarnings: partner.totalEarnings || 0,
        });
      }
    } catch (error) {
      console.error('Error checking referral status:', error);
    } finally {
      setIsChecking(false);
    }
  };

  const handleActivate = async () => {
    setIsActivating(true);
    try {
      const partner = await referralApi.activateForCompany();
      setIsActive(true);
      setReferralCode(partner.referralCode);
      showToast({
        title: 'Partner program activated!',
        message: 'You can now earn commissions by referring candidates.',
        type: 'success',
      });

      // Refresh the page to update navigation
      window.location.reload();
    } catch (error) {
      console.error('Failed to activate partner program:', error);
      showToast({
        title: 'Failed to activate partner program',
        message: 'Please try again.',
        type: 'error',
      });
    } finally {
      setIsActivating(false);
    }
  };

  const goToPartnerDashboard = () => {
    router.push('/referral-partner');
  };

  if (isChecking) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="animate-pulse space-y-3">
            <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (isActive && referralCode) {
    return (
      <Card className="border-green-200 bg-gradient-to-br from-green-50 to-emerald-50">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-green-800">Partner Program Active</CardTitle>
              <CardDescription className="text-green-700 mt-2">
                Your referral code: <strong className="font-mono text-lg">{referralCode}</strong>
              </CardDescription>
            </div>
            <Badge variant="default" className="bg-green-600">
              <CheckCircle2 className="h-4 w-4 mr-1" />
              Active
            </Badge>
          </div>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card className="bg-white/50">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">Total Referrals</p>
                    <p className="text-2xl font-bold text-green-800">
                      {partnerStats.totalReferrals}
                    </p>
                  </div>
                  <Users className="h-8 w-8 text-green-600" />
                </div>
              </CardContent>
            </Card>

            <Card className="bg-white/50">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">Successful Hires</p>
                    <p className="text-2xl font-bold text-green-800">
                      {partnerStats.successfulPlacements}
                    </p>
                  </div>
                  <CheckCircle2 className="h-8 w-8 text-green-600" />
                </div>
              </CardContent>
            </Card>

            <Card className="bg-white/50">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">Total Earnings</p>
                    <p className="text-2xl font-bold text-green-800">
                      ${partnerStats.totalEarnings}
                    </p>
                  </div>
                  <DollarSign className="h-8 w-8 text-green-600" />
                </div>
              </CardContent>
            </Card>
          </div>

          <Alert className="bg-blue-50 border-blue-200">
            <AlertCircle className="h-4 w-4 text-blue-600" />
            <AlertTitle className="text-blue-800">How it works</AlertTitle>
            <AlertDescription className="text-blue-700">
              Share job openings from other companies with your network. When your referrals get
              hired, you earn commission. Track everything in your partner dashboard.
            </AlertDescription>
          </Alert>
        </CardContent>
        <CardFooter className="flex gap-3">
          <Button onClick={goToPartnerDashboard} className="flex-1">
            <BarChart3 className="h-4 w-4 mr-2" />
            Go to Partner Dashboard
          </Button>
          <Button variant="outline" className="flex-1">
            <Link className="h-4 w-4 mr-2" />
            Get Referral Links
          </Button>
        </CardFooter>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center gap-3">
          <Building2 className="h-6 w-6 text-purple-600" />
          <div>
            <CardTitle>Referral Partner Program</CardTitle>
            <CardDescription>
              Earn commissions by referring candidates to other companies
            </CardDescription>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="prose prose-sm text-gray-600">
          <p>
            As a company on our platform, you can also participate in our referral program. This
            allows you to earn commissions by referring qualified candidates to positions at other
            companies.
          </p>
        </div>

        <Separator />

        <div className="space-y-4">
          <h3 className="font-semibold text-lg">Program Benefits</h3>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0">
                <div className="h-10 w-10 rounded-full bg-purple-100 flex items-center justify-center">
                  <DollarSign className="h-6 w-6 text-purple-600" />
                </div>
              </div>
              <div>
                <p className="font-medium">Competitive Commissions</p>
                <p className="text-sm text-gray-600">Earn up to 10% on successful placements</p>
              </div>
            </div>

            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0">
                <div className="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                  <Users className="h-6 w-6 text-blue-600" />
                </div>
              </div>
              <div>
                <p className="font-medium">Leverage Your Network</p>
                <p className="text-sm text-gray-600">Help talented professionals in your network</p>
              </div>
            </div>

            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0">
                <div className="h-10 w-10 rounded-full bg-green-100 flex items-center justify-center">
                  <TrendingUp className="h-6 w-6 text-green-600" />
                </div>
              </div>
              <div>
                <p className="font-medium">Track Performance</p>
                <p className="text-sm text-gray-600">Real-time analytics and reporting</p>
              </div>
            </div>

            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0">
                <div className="h-10 w-10 rounded-full bg-orange-100 flex items-center justify-center">
                  <Link className="h-6 w-6 text-orange-600" />
                </div>
              </div>
              <div>
                <p className="font-medium">Easy Sharing</p>
                <p className="text-sm text-gray-600">Custom referral links for each opportunity</p>
              </div>
            </div>
          </div>
        </div>

        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>No Conflict of Interest</AlertTitle>
          <AlertDescription>
            This program is designed for referring candidates to other companies' positions. You
            cannot earn referral commissions for your own job postings.
          </AlertDescription>
        </Alert>
      </CardContent>
      <CardFooter>
        <Button onClick={handleActivate} disabled={isActivating} className="w-full" size="lg">
          {isActivating ? 'Activating Partner Program...' : 'Activate Partner Program'}
        </Button>
      </CardFooter>
    </Card>
  );
};
