'use client';

import { isPublicRoute } from '@/types/publicRoutes';
import { UserProvider, UserProviderProps } from '@auth0/nextjs-auth0/client';
import { usePathname } from 'next/navigation';
import React from 'react';

interface Auth0ProviderWithOptionsProps {
  children: React.ReactNode;
}

/**
 * Custom Auth0 Provider that disables user fetching on public routes
 */
export default function Auth0ProviderWithOptions({ children }: Auth0ProviderWithOptionsProps) {
  const pathname = usePathname();
  const isOnPublicRoute = isPublicRoute(pathname);

  // Configure Auth0 provider options based on route type
  const providerProps: UserProviderProps = {
    // Disable profile fetching on public routes
    profileUrl: isOnPublicRoute ? undefined : '/api/auth/me',
    // Skip initial fetch on public routes
    fetcher: isOnPublicRoute ? async () => ({ user: undefined }) : undefined,
  };

  return <UserProvider {...providerProps}>{children}</UserProvider>;
}
