'use client';

import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { DollarSign, Users, TrendingUp, Link } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { showToast } from '@/components/Toaster';
import { referralApi } from '@/app/referral-partner/services/referralApi';

export const ReferralProgramCard: React.FC = () => {
  const router = useRouter();
  const [isActivating, setIsActivating] = useState(false);
  const [isChecking, setIsChecking] = useState(true);
  const [isActive, setIsActive] = useState(false);
  const [referralCode, setReferralCode] = useState<string | null>(null);

  useEffect(() => {
    checkReferralStatus();
  }, []);

  const checkReferralStatus = async () => {
    try {
      const { isActive: active, partner } = await referralApi.checkReferralStatus();
      setIsActive(active);
      if (partner) {
        setReferralCode(partner.referralCode);
      }
    } catch (error) {
      console.error('Error checking referral status:', error);
    } finally {
      setIsChecking(false);
    }
  };

  const handleActivate = async () => {
    setIsActivating(true);
    try {
      const partner = await referralApi.activateForJobSeeker();
      setIsActive(true);
      setReferralCode(partner.referralCode);
      showToast({
        title: 'Referral program activated!',
        message: 'You can now start earning by referring candidates.',
        type: 'success',
      });

      // Refresh the page to update navigation
      window.location.reload();
    } catch (error) {
      console.error('Failed to activate referral program:', error);
      showToast({
        title: 'Failed to activate referral program',
        message: 'Please try again.',
        type: 'error',
      });
    } finally {
      setIsActivating(false);
    }
  };

  const goToReferralDashboard = () => {
    router.push('/referral-partner');
  };

  if (isChecking) {
    return (
      <Card className="w-full">
        <CardContent className="p-6">
          <div className="animate-pulse space-y-3">
            <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (isActive && referralCode) {
    return (
      <Card className="w-full bg-gradient-to-br from-green-50 to-emerald-50 border-green-200">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-green-800">Referral Program Active</CardTitle>
            <Badge variant="default" className="bg-green-600">
              Active
            </Badge>
          </div>
          <CardDescription className="text-green-700">
            Your referral code: <strong className="font-mono">{referralCode}</strong>
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-3 gap-4 mb-4">
            <div className="text-center">
              <Users className="h-8 w-8 text-green-600 mx-auto mb-2" />
              <p className="text-sm text-gray-600">Share Jobs</p>
            </div>
            <div className="text-center">
              <TrendingUp className="h-8 w-8 text-green-600 mx-auto mb-2" />
              <p className="text-sm text-gray-600">Track Referrals</p>
            </div>
            <div className="text-center">
              <DollarSign className="h-8 w-8 text-green-600 mx-auto mb-2" />
              <p className="text-sm text-gray-600">Earn Commission</p>
            </div>
          </div>
        </CardContent>
        <CardFooter>
          <Button onClick={goToReferralDashboard} className="w-full">
            Go to Referral Dashboard
          </Button>
        </CardFooter>
      </Card>
    );
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Earn by Referring Candidates</CardTitle>
        <CardDescription>
          Help others find great jobs and earn commission for successful placements
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0">
              <div className="h-8 w-8 rounded-full bg-purple-100 flex items-center justify-center">
                <DollarSign className="h-5 w-5 text-purple-600" />
              </div>
            </div>
            <div>
              <p className="font-medium text-sm">Earn Commissions</p>
              <p className="text-sm text-gray-600">Up to 10% on successful placements</p>
            </div>
          </div>

          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0">
              <div className="h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center">
                <Link className="h-5 w-5 text-blue-600" />
              </div>
            </div>
            <div>
              <p className="font-medium text-sm">Easy Sharing</p>
              <p className="text-sm text-gray-600">Get unique referral links for jobs</p>
            </div>
          </div>

          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0">
              <div className="h-8 w-8 rounded-full bg-green-100 flex items-center justify-center">
                <TrendingUp className="h-5 w-5 text-green-600" />
              </div>
            </div>
            <div>
              <p className="font-medium text-sm">Track Progress</p>
              <p className="text-sm text-gray-600">Real-time referral tracking</p>
            </div>
          </div>

          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0">
              <div className="h-8 w-8 rounded-full bg-orange-100 flex items-center justify-center">
                <Users className="h-5 w-5 text-orange-600" />
              </div>
            </div>
            <div>
              <p className="font-medium text-sm">Help Your Network</p>
              <p className="text-sm text-gray-600">Connect talent with opportunities</p>
            </div>
          </div>
        </div>

        <Alert className="bg-blue-50 border-blue-200">
          <AlertDescription className="text-blue-800">
            Join our referral program and start earning today. Share job opportunities with your
            network and earn commissions when your referrals get hired.
          </AlertDescription>
        </Alert>
      </CardContent>
      <CardFooter>
        <Button onClick={handleActivate} disabled={isActivating} className="w-full" size="lg">
          {isActivating ? 'Activating...' : 'Start Earning Now'}
        </Button>
      </CardFooter>
    </Card>
  );
};
