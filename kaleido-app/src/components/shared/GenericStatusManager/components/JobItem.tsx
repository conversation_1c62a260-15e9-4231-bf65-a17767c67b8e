import React from 'react';
import { AnimatePresence, motion } from 'framer-motion';
import { AlertCircle, CheckCircle, X, XCircle } from 'lucide-react';
import { StatusJob } from '../types';
import { getEffectiveStatus } from '../utils/statusHelpers';
import { canCancelJob, canRemoveJob } from '../utils/jobUtils';
import CompletionDetailsRenderer from '../CompletionDetailsRenderer';
import { ModernProgress } from '@/components/ui/modern-progress';

interface JobItemProps {
  job: StatusJob;
  isExpanded: boolean;
  onToggleExpanded: () => void;
  onCancel: (jobId: string) => void;
  onRemove: (jobId: string) => void;
  cancelEndpoint?: (jobId: string) => string;
  completionDetailsConfig?: any;
  renderJobDetails?: (job: StatusJob) => React.ReactNode;
  showViewResultsButton?: boolean;
  onViewResults?: (jobId: string, job: StatusJob) => void;
  viewResultsButtonText?: string;
}

export const JobItem: React.FC<JobItemProps> = ({
  job,
  isExpanded,
  onToggleExpanded,
  onCancel,
  onRemove,
  cancelEndpoint,
  completionDetailsConfig,
  renderJobDetails,
  showViewResultsButton,
  onViewResults,
  viewResultsButtonText = 'View Results',
}) => {
  const effectiveStatus = getEffectiveStatus(job);
  const isCompleted =
    effectiveStatus === 'completed' || effectiveStatus === 'completed_with_errors';

  const renderStatusIcon = () => {
    switch (effectiveStatus) {
      case 'completed':
        return <CheckCircle className="text-green-500 h-5 w-5" />;
      case 'completed_with_errors':
        return <AlertCircle className="text-yellow-400 h-5 w-5" />;
      case 'failed':
      case 'error':
        return <XCircle className="text-red-500 h-5 w-5" />;
      case 'cancelled':
        return <AlertCircle className="text-yellow-500 h-5 w-5" />;
      default:
        return (
          <div className="relative w-5 h-5">
            <div className="absolute inset-0 rounded-full border-2 border-pink-700 border-t-transparent animate-spin"></div>
          </div>
        );
    }
  };

  const getStatusText = () => {
    switch (effectiveStatus) {
      case 'completed':
        return 'Complete';
      case 'completed_with_errors':
        return 'Completed (with Issues)';
      case 'failed':
        return 'Failed';
      case 'cancelled':
        return 'Cancelled';
      case 'queued':
        return 'Queued';
      default:
        return 'Processing';
    }
  };

  return (
    <div className="bg-white/5 p-3 rounded-lg border border-white/10">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          {renderStatusIcon()}
          <span className="text-white text-sm font-medium">{getStatusText()}</span>
        </div>
        <div className="flex items-center gap-2">
          {job.progress !== undefined && (
            <span className="text-white/70 text-sm">{Math.round(job.progress)}%</span>
          )}
          {canCancelJob(job) && cancelEndpoint && (
            <button
              type="button"
              onClick={() => onCancel(job.id)}
              className="text-red-400 hover:text-red-300 transition-colors"
              title="Cancel job"
            >
              <X size={16} />
            </button>
          )}
          {canRemoveJob(job) && (
            <button
              type="button"
              onClick={() => onRemove(job.id)}
              className="text-gray-400 hover:text-red-300 transition-colors"
              title="Remove from list"
            >
              <X size={16} />
            </button>
          )}
        </div>
      </div>

      {job.progress !== undefined && !isCompleted && (
        <div className="mt-3">
          <ModernProgress
            value={job.progress}
            variant="gradient"
            showPercentage={false}
            className="mb-2"
          />
        </div>
      )}

      {job.message && <p className="text-white/70 text-sm mt-2">{job.message}</p>}

      {isCompleted && showViewResultsButton && onViewResults && (
        <button
          type="button"
          onClick={() => onViewResults(job.id, job)}
          className="mt-2 px-3 py-1 bg-purple-600 hover:bg-purple-700 text-white text-sm rounded transition-colors"
        >
          {viewResultsButtonText}
        </button>
      )}

      <AnimatePresence>
        {isExpanded &&
          (completionDetailsConfig ? (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: 'auto', opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              className="overflow-hidden"
            >
              <CompletionDetailsRenderer
                job={job}
                config={completionDetailsConfig}
                isExpanded={isExpanded}
                onToggleExpanded={onToggleExpanded}
              />
            </motion.div>
          ) : renderJobDetails ? (
            renderJobDetails(job)
          ) : null)}
      </AnimatePresence>
    </div>
  );
};
