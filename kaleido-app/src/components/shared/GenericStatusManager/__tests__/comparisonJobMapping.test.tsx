import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import { GenericStatusManager } from '../GenericStatusManager';
import { createComparisonConfig } from '../configs/comparisonConfig';
import { StatusJob } from '../types';
import apiHelper from '@/lib/apiHelper';
import { useStatusManagerStore } from '@/stores/statusManagerStore';

// Mock dependencies
jest.mock('@/lib/apiHelper');
jest.mock('@/stores/statusManagerStore');

const mockApiHelper = apiHelper as jest.Mocked<typeof apiHelper>;
const mockUseStatusManagerStore = useStatusManagerStore as unknown as jest.MockedFunction<
  typeof useStatusManagerStore
>;

describe('Comparison Job ID Mapping', () => {
  const mockOnUpdateJob = jest.fn();
  const mockOnRemoveJob = jest.fn();
  const mockOnClearCompleted = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();

    mockUseStatusManagerStore.mockReturnValue({
      instances: {},
      modalState: { isOpen: false, isSameUrl: false },
      createInstance: jest.fn(),
      removeInstance: jest.fn(),
      updateJob: jest.fn(),
      removeJob: jest.fn(),
      showCompletionModal: jest.fn(),
      hideCompletionModal: jest.fn(),
      setInstanceMinimized: jest.fn(),
      setInstanceCollapsed: jest.fn(),
      clearCompletionCache: jest.fn(),
      ensureSingleInstance: jest.fn(),
    } as any);

    // Mock the markAsPollingEndpoint and debugEndpoint methods
    mockApiHelper.markAsPollingEndpoint = jest.fn();
    mockApiHelper.debugEndpoint = jest.fn().mockReturnValue({});
  });

  it('should use Bull queue job ID for polling status endpoint', async () => {
    const comparisonJob: StatusJob = {
      id: 'bull-queue-job-123', // Bull queue job ID (no jobId property)
      status: 'processing',
      progress: 0,
      message: 'Processing comparison',
      metadata: {
        comparisonEntityId: 'comparison-entity-456',
      },
      result: {
        comparisonId: 'comparison-entity-456',
      },
    };

    mockApiHelper.get.mockResolvedValue({
      status: 'processing',
      progress: 25,
      message: 'Analyzing candidates',
    });

    render(
      <GenericStatusManager
        jobs={{ 'bull-queue-job-123': comparisonJob }}
        activeJobs={['bull-queue-job-123']}
        config={createComparisonConfig()}
        onUpdateJob={mockOnUpdateJob}
        onRemoveJob={mockOnRemoveJob}
        onClearCompleted={mockOnClearCompleted}
      />
    );

    // Wait for the polling to occur
    await waitFor(() => {
      // Since no jobId property, polling uses the id
      expect(mockApiHelper.get).toHaveBeenCalledWith(
        '/comparisons/status/bull-queue-job-123',
        expect.any(Object)
      );
    });
  });

  it('should use comparison entity ID for navigation and display', async () => {
    const comparisonJob: StatusJob = {
      id: 'bull-queue-job-123',
      status: 'completed',
      progress: 100,
      result: {
        comparisonId: 'comparison-entity-456',
        executiveSummary: 'Comparison complete',
      },
    };

    const config = createComparisonConfig();
    const actualJobId = config.getActualJobId!(comparisonJob);

    // Should return the comparison entity ID
    expect(actualJobId).toBe('comparison-entity-456');
  });

  it('should handle fallback when result.comparisonId is not available', () => {
    const comparisonJob: StatusJob = {
      id: 'bull-queue-job-123',
      status: 'completed',
      progress: 100,
      result: {}, // No comparisonId in result
      metadata: {
        comparisonEntityId: 'comparison-entity-456',
      },
    };

    const config = createComparisonConfig();
    const actualJobId = config.getActualJobId!(comparisonJob);

    // Should fallback to id since jobId is not set
    expect(actualJobId).toBe('bull-queue-job-123');
  });

  it('should handle final fallback to id when both comparisonId and jobId are missing', () => {
    const comparisonJob: StatusJob = {
      id: 'bull-queue-job-123',
      status: 'completed',
      progress: 100,
      // No jobId property
    };

    const config = createComparisonConfig();
    const actualJobId = config.getActualJobId!(comparisonJob);

    // Should fallback to id
    expect(actualJobId).toBe('bull-queue-job-123');
  });

  it('should poll with Bull queue job ID when no jobId property exists', async () => {
    const comparisonJob: StatusJob = {
      id: 'bull-queue-job-123',
      status: 'processing',
      progress: 0,
      metadata: {
        comparisonEntityId: 'comparison-entity-456',
      },
    };

    mockApiHelper.get.mockResolvedValue({
      status: 'processing',
      progress: 50,
    });

    render(
      <GenericStatusManager
        jobs={{ 'bull-queue-job-123': comparisonJob }}
        activeJobs={['bull-queue-job-123']}
        config={createComparisonConfig()}
        onUpdateJob={mockOnUpdateJob}
        onRemoveJob={mockOnRemoveJob}
      />
    );

    await waitFor(() => {
      // Should use the job.id (Bull queue ID) for polling
      expect(mockApiHelper.get).toHaveBeenCalledWith(
        '/comparisons/status/bull-queue-job-123',
        expect.any(Object)
      );
    });
  });

  it('should navigate to correct URL using job ID on view results', () => {
    const comparisonJob: StatusJob = {
      id: 'bull-queue-job-123',
      status: 'completed',
      result: {
        comparisonId: 'comparison-entity-456',
      },
      metadata: {
        jobId: 'job-789', // This is the job entity ID for navigation
      },
    };

    // Mock window.location
    delete (window as any).location;
    window.location = { href: '' } as any;

    const config = createComparisonConfig();
    config.onViewResults!('bull-queue-job-123', comparisonJob);

    expect(window.location.href).toBe('/jobs/job-789/candidates?tab=comparisons');
  });

  it('should update job with correct progress from polling response', async () => {
    const comparisonJob: StatusJob = {
      id: 'bull-queue-job-123',
      status: 'processing',
      progress: 0,
    };

    mockApiHelper.get.mockResolvedValue({
      status: 'processing',
      progress: 75,
      message: 'Generating recommendations',
    });

    render(
      <GenericStatusManager
        jobs={{ 'bull-queue-job-123': comparisonJob }}
        activeJobs={['bull-queue-job-123']}
        config={createComparisonConfig()}
        onUpdateJob={mockOnUpdateJob}
        onRemoveJob={mockOnRemoveJob}
      />
    );

    await waitFor(() => {
      expect(mockOnUpdateJob).toHaveBeenCalledWith(
        'bull-queue-job-123',
        expect.objectContaining({
          progress: 75,
          message: 'Generating recommendations',
        })
      );
    });
  });

  it('should handle comparison completion with proper ID mapping', async () => {
    const comparisonJob: StatusJob = {
      id: 'bull-queue-job-123',
      status: 'processing',
      progress: 90,
    };

    const completionResult = {
      comparisonId: 'comparison-entity-456',
      executiveSummary: 'Comparison analysis complete',
      candidateCount: 3,
      recommendations: {
        topChoice: {
          candidateId: 'candidate-1',
          candidateName: 'John Doe',
          reasoning: 'Best match for the role',
        },
      },
    };

    mockApiHelper.get.mockResolvedValue({
      status: 'completed',
      progress: 100,
      result: completionResult,
    });

    render(
      <GenericStatusManager
        jobs={{ 'bull-queue-job-123': comparisonJob }}
        activeJobs={['bull-queue-job-123']}
        config={createComparisonConfig()}
        onUpdateJob={mockOnUpdateJob}
        onRemoveJob={mockOnRemoveJob}
      />
    );

    await waitFor(() => {
      expect(mockOnUpdateJob).toHaveBeenCalledWith(
        'bull-queue-job-123',
        expect.objectContaining({
          status: 'completed',
          progress: 100,
          result: completionResult,
        })
      );
    });
  });
});

describe('Comparison Job Creation', () => {
  it('should create status job with correct ID mapping from API response', () => {
    const apiResponse = {
      id: 'comparison-entity-789', // Entity ID
      jobId: 'bull-queue-job-999', // Bull queue job ID
      status: 'queued',
      message: 'Comparison queued for processing',
    };

    // This is how the status job should be created based on the API response
    const statusJob: StatusJob = {
      id: apiResponse.jobId, // Bull queue job ID for polling
      status: apiResponse.status as StatusJob['status'],
      progress: 0,
      message: apiResponse.message,
      metadata: {
        comparisonEntityId: apiResponse.id, // Store entity ID in metadata
      },
      result: {
        comparisonId: apiResponse.id, // Store entity ID in result for navigation
      },
    };

    expect(statusJob.id).toBe('bull-queue-job-999');
    expect(statusJob.metadata?.comparisonEntityId).toBe('comparison-entity-789');
    expect(statusJob.result?.comparisonId).toBe('comparison-entity-789');
  });
});
