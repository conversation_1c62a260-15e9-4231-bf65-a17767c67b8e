import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { GenericStatusManager } from '../GenericStatusManager';
import { StatusJob, StatusManagerConfig } from '../types';
import apiHelper from '@/lib/apiHelper';
import { useStatusManagerStore } from '@/stores/statusManagerStore';

// Mock dependencies
jest.mock('@/lib/apiHelper');
jest.mock('@/stores/statusManagerStore');

const mockApiHelper = apiHelper as jest.Mocked<typeof apiHelper>;
const mockUseStatusManagerStore = useStatusManagerStore as unknown as jest.MockedFunction<
  typeof useStatusManagerStore
>;

describe('GenericStatusManager - Delete All Functionality', () => {
  const mockOnUpdateJob = jest.fn();
  const mockOnRemoveJob = jest.fn();
  const mockOnClearCompleted = jest.fn();
  const mockRemoveStoreJob = jest.fn();
  const mockRemoveInstance = jest.fn();
  const mockClearCompletionCache = jest.fn();

  const defaultConfig: StatusManagerConfig = {
    title: 'Test Manager',
    icon: <span>Icon</span>,
    statusEndpoint: jobId => `/status/${jobId}`,
    cancelEndpoint: jobId => `/cancel/${jobId}`,
    pollInterval: 5000,
    maxRetries: 3,
    managerId: 'test-manager',
  };

  const activeJob: StatusJob = {
    id: 'job1',
    status: 'processing',
    progress: 50,
    message: 'Processing...',
  };

  const waitingJob: StatusJob = {
    id: 'job2',
    status: 'waiting',
    progress: 0,
    message: 'Waiting...',
  };

  const completedJob: StatusJob = {
    id: 'job3',
    status: 'completed',
    progress: 100,
    message: 'Completed',
  };

  beforeEach(() => {
    jest.clearAllMocks();

    mockUseStatusManagerStore.mockReturnValue({
      instances: {},
      modalState: { isOpen: false, isSameUrl: false },
      createInstance: jest.fn(),
      removeInstance: mockRemoveInstance,
      updateJob: jest.fn(),
      removeJob: mockRemoveStoreJob,
      showCompletionModal: jest.fn(),
      hideCompletionModal: jest.fn(),
      setInstanceMinimized: jest.fn(),
      setInstanceCollapsed: jest.fn(),
      clearCompletionCache: mockClearCompletionCache,
      ensureSingleInstance: jest.fn(),
    } as any);

    // Mock getState to return the store methods
    (mockUseStatusManagerStore as any).getState = jest.fn().mockReturnValue({
      clearCompletionCache: mockClearCompletionCache,
    });

    mockApiHelper.post.mockResolvedValue({});
  });

  it('should render the delete all button', () => {
    render(
      <GenericStatusManager
        jobs={{ job1: activeJob }}
        activeJobs={['job1']}
        config={defaultConfig}
        onUpdateJob={mockOnUpdateJob}
        onRemoveJob={mockOnRemoveJob}
        onClearCompleted={mockOnClearCompleted}
      />
    );

    const deleteButton = screen.getByTitle('Delete all test manager jobs');
    expect(deleteButton).toBeInTheDocument();
  });

  it('should cancel all active jobs when delete all is clicked', async () => {
    const user = userEvent.setup();

    render(
      <GenericStatusManager
        jobs={{
          job1: activeJob,
          job2: waitingJob,
          job3: completedJob,
        }}
        activeJobs={['job1', 'job2', 'job3']}
        config={defaultConfig}
        onUpdateJob={mockOnUpdateJob}
        onRemoveJob={mockOnRemoveJob}
        onClearCompleted={mockOnClearCompleted}
      />
    );

    const deleteButton = screen.getByTitle('Delete all test manager jobs');
    await user.click(deleteButton);

    await waitFor(() => {
      // Should cancel only active and waiting jobs, not completed ones
      expect(mockApiHelper.post).toHaveBeenCalledWith('/cancel/job1', {});
      expect(mockApiHelper.post).toHaveBeenCalledWith('/cancel/job2', {});
      expect(mockApiHelper.post).toHaveBeenCalledTimes(2);
    });
  });

  it('should remove all jobs from state when delete all is clicked', async () => {
    const user = userEvent.setup();

    render(
      <GenericStatusManager
        jobs={{
          job1: activeJob,
          job2: completedJob,
        }}
        activeJobs={['job1', 'job2']}
        config={defaultConfig}
        onUpdateJob={mockOnUpdateJob}
        onRemoveJob={mockOnRemoveJob}
        onClearCompleted={mockOnClearCompleted}
      />
    );

    const deleteButton = screen.getByTitle('Delete all test manager jobs');
    await user.click(deleteButton);

    await waitFor(() => {
      // Should remove all jobs
      expect(mockOnRemoveJob).toHaveBeenCalledWith('job1');
      expect(mockOnRemoveJob).toHaveBeenCalledWith('job2');
      expect(mockRemoveStoreJob).toHaveBeenCalledWith('test-manager', 'job1');
      expect(mockRemoveStoreJob).toHaveBeenCalledWith('test-manager', 'job2');
    });
  });

  it('should clear completion cache and remove instance', async () => {
    const user = userEvent.setup();

    render(
      <GenericStatusManager
        jobs={{ job1: activeJob }}
        activeJobs={['job1']}
        config={defaultConfig}
        onUpdateJob={mockOnUpdateJob}
        onRemoveJob={mockOnRemoveJob}
        onClearCompleted={mockOnClearCompleted}
      />
    );

    const deleteButton = screen.getByTitle('Delete all test manager jobs');
    await user.click(deleteButton);

    await waitFor(() => {
      expect(mockRemoveInstance).toHaveBeenCalledWith('test-manager');
      expect(mockClearCompletionCache).toHaveBeenCalled();
      expect(mockOnClearCompleted).toHaveBeenCalled();
    });
  });

  it('should handle errors when canceling jobs', async () => {
    const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation();
    mockApiHelper.post.mockRejectedValueOnce(new Error('Cancel failed'));

    const user = userEvent.setup();

    render(
      <GenericStatusManager
        jobs={{ job1: activeJob }}
        activeJobs={['job1']}
        config={defaultConfig}
        onUpdateJob={mockOnUpdateJob}
        onRemoveJob={mockOnRemoveJob}
        onClearCompleted={mockOnClearCompleted}
      />
    );

    const deleteButton = screen.getByTitle('Delete all test manager jobs');
    await user.click(deleteButton);

    await waitFor(() => {
      expect(consoleErrorSpy).toHaveBeenCalledWith('Failed to cancel job job1:', expect.any(Error));
      // Should still remove the job even if cancel fails
      expect(mockOnRemoveJob).toHaveBeenCalledWith('job1');
    });

    consoleErrorSpy.mockRestore();
  });

  it('should work without cancel endpoint', async () => {
    const configWithoutCancel = { ...defaultConfig, cancelEndpoint: undefined };
    const user = userEvent.setup();

    render(
      <GenericStatusManager
        jobs={{ job1: activeJob }}
        activeJobs={['job1']}
        config={configWithoutCancel}
        onUpdateJob={mockOnUpdateJob}
        onRemoveJob={mockOnRemoveJob}
        onClearCompleted={mockOnClearCompleted}
      />
    );

    const deleteButton = screen.getByTitle('Delete all test manager jobs');
    await user.click(deleteButton);

    await waitFor(() => {
      // Should not try to cancel
      expect(mockApiHelper.post).not.toHaveBeenCalled();
      // But should still remove jobs
      expect(mockOnRemoveJob).toHaveBeenCalledWith('job1');
    });
  });

  it('should close the manager after delete all', async () => {
    const user = userEvent.setup();

    const { container } = render(
      <GenericStatusManager
        jobs={{ job1: activeJob }}
        activeJobs={['job1']}
        config={defaultConfig}
        onUpdateJob={mockOnUpdateJob}
        onRemoveJob={mockOnRemoveJob}
        onClearCompleted={mockOnClearCompleted}
      />
    );

    expect(container.firstChild).not.toBeNull();

    const deleteButton = screen.getByTitle('Delete all test manager jobs');
    await user.click(deleteButton);

    // Manager should be closed after delete all
    await waitFor(() => {
      expect(container.firstChild).toBeNull();
    });
  });
});
