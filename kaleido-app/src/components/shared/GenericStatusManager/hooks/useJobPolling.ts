import { useEffect, useRef } from 'react';

import apiHelper from '@/lib/apiHelper';

import { StatusJob } from '../types';
import { resolveStatus } from '../utils/statusHelpers';

interface UseJobPollingProps {
  jobs: Record<string, StatusJob>;
  activeJobs: string[];
  statusEndpoint: (jobId: string) => string;
  pollInterval: number;
  maxRetries: number;
  onUpdateJob: (jobId: string, updates: Partial<StatusJob>) => void;
  onJobComplete?: (jobId: string, result: any) => void;
  onJobFailed?: (jobId: string, error: any) => void;
  enableManagerCoordination?: boolean;
  managerId: string;
  clearOtherManagers: () => void;
}

export const useJobPolling = ({
  jobs,
  activeJobs,
  statusEndpoint,
  pollInterval,
  maxRetries,
  onUpdateJob,
  onJobComplete,
  onJobFailed,
  enableManagerCoordination,
  managerId,
  clearOtherManagers,
}: UseJobPollingProps) => {
  // Use refs to store callback functions to avoid dependency issues
  const onUpdateJobRef = useRef(onUpdateJob);
  const onJobCompleteRef = useRef(onJobComplete);
  const onJobFailedRef = useRef(onJobFailed);
  const clearOtherManagersRef = useRef(clearOtherManagers);

  // Update refs when callbacks change
  onUpdateJobRef.current = onUpdateJob;
  onJobCompleteRef.current = onJobComplete;
  onJobFailedRef.current = onJobFailed;
  clearOtherManagersRef.current = clearOtherManagers;

  // Store polling intervals, job refs, and error counts for exponential backoff
  const pollingIntervalsRef = useRef<Map<string, NodeJS.Timeout>>(new Map());
  const jobsRef = useRef(jobs);
  const errorCountsRef = useRef<Map<string, number>>(new Map());
  jobsRef.current = jobs;

  // Function to poll a single job
  const pollJob = async (jobId: string) => {
    // Get the latest job state from ref
    const currentJob = jobsRef.current[jobId];

    // Stop polling if job doesn't exist or is no longer in polling state
    if (
      !currentJob ||
      (currentJob.status !== 'queued' &&
        currentJob.status !== 'active' &&
        currentJob.status !== 'processing')
    ) {
      const interval = pollingIntervalsRef.current.get(jobId);
      if (interval) {
        clearInterval(interval);
        pollingIntervalsRef.current.delete(jobId);
      }
      return;
    }

    try {
      // Use job.jobId if available, otherwise use the key (jobId)
      // This handles cases where the job is stored with a different key than its actual jobId
      const actualJobId = currentJob.jobId || jobId;

      const endpoint = statusEndpoint(actualJobId);

      // Mark as polling endpoint to ensure it bypasses rate limiting
      apiHelper.markAsPollingEndpoint(endpoint);

      // Debug the endpoint status
      const debugInfo = apiHelper.debugEndpoint(endpoint);

      const response = await apiHelper.get(endpoint, {
        isPollingRequest: true,
        bypassCircuitBreaker: true, // Explicitly bypass circuit breaker
        skipDedupe: true, // Skip request deduplication to ensure each poll is fresh
      });

      // Reset error count on successful response
      errorCountsRef.current.delete(jobId);

      if (response?.status === 'not_found' || response?.error === 'Job not found') {
        onUpdateJobRef.current(jobId, {
          status: 'cancelled',
          message: 'Job no longer exists on server',
        });
        const interval = pollingIntervalsRef.current.get(jobId);
        if (interval) {
          clearInterval(interval);
          pollingIntervalsRef.current.delete(jobId);
        }
        return;
      }

      if (response) {
        const { status: rawStatus, progress, result, message } = response;

        const resolvedStatus = resolveStatus(rawStatus as StatusJob['status'], progress);

        // Prepare job updates
        const jobUpdates: Partial<StatusJob> = {};

        // Update progress if provided
        if (progress !== undefined) {
          jobUpdates.progress = progress;
        }

        // Update status and related fields if changed
        if (resolvedStatus !== currentJob.status) {
          jobUpdates.status = resolvedStatus;
          jobUpdates.result = result;
          jobUpdates.message = message;
        }

        // Always update message if it changed (for progress updates)
        if (message !== currentJob.message && message) {
          jobUpdates.message = message;
        }

        // Treat completed statuses (including completed_with_errors) as terminal states
        if (resolvedStatus === 'completed' || resolvedStatus === 'completed_with_errors') {
          jobUpdates.status = resolvedStatus;
          jobUpdates.progress = 100; // Force 100% for display purposes
          jobUpdates.result = result;
          jobUpdates.message = message;

          // Close other managers immediately when this job completes (singleton pattern)
          if (enableManagerCoordination) {
            clearOtherManagersRef.current();
          }
        }

        // Apply all updates at once to avoid race conditions
        if (Object.keys(jobUpdates).length > 0) {
          onUpdateJobRef.current(jobId, jobUpdates);
        } else {
        }

        // Handle completion (including completed_with_errors)
        if (resolvedStatus === 'completed' || resolvedStatus === 'completed_with_errors') {
          if (onJobCompleteRef.current) {
            onJobCompleteRef.current(jobId, result);
          }
          const interval = pollingIntervalsRef.current.get(jobId);
          if (interval) {
            clearInterval(interval);
            pollingIntervalsRef.current.delete(jobId);
          }
        }

        // Handle failure
        if (rawStatus === 'failed' || rawStatus === 'cancelled' || rawStatus === 'error') {
          if (rawStatus === 'failed' && onJobFailedRef.current) {
            onJobFailedRef.current(jobId, result || message);
          }
          const interval = pollingIntervalsRef.current.get(jobId);
          if (interval) {
            clearInterval(interval);
            pollingIntervalsRef.current.delete(jobId);
          }
        }
      }
    } catch (error) {
      console.error(`❌ [Polling] Error fetching job status for job ${jobId}:`, error);
      console.error(`❌ [Polling] Error details:`, {
        endpoint: statusEndpoint(jobId),
        errorMessage: error instanceof Error ? error.message : 'Unknown error',
        errorStack: error instanceof Error ? error.stack : undefined,
      });

      if (error.response?.status === 404) {
        onUpdateJobRef.current(jobId, {
          status: 'cancelled',
          message: 'Job no longer exists on server',
        });
        const interval = pollingIntervalsRef.current.get(jobId);
        if (interval) {
          clearInterval(interval);
          pollingIntervalsRef.current.delete(jobId);
        }
        return;
      }

      // Increment error count for exponential backoff
      const currentErrors = errorCountsRef.current.get(jobId) || 0;
      const newErrorCount = currentErrors + 1;
      errorCountsRef.current.set(jobId, newErrorCount);

      // Also update job error count
      const errorCount = (currentJob.errorCount || 0) + 1;
      onUpdateJobRef.current(jobId, { errorCount });

      if (errorCount > maxRetries) {
        onUpdateJobRef.current(jobId, {
          status: 'failed',
          message: 'Unable to get job status from server',
          error: error instanceof Error ? error.message : 'Unknown error',
        });
        const interval = pollingIntervalsRef.current.get(jobId);
        if (interval) {
          clearInterval(interval);
          pollingIntervalsRef.current.delete(jobId);
        }
        errorCountsRef.current.delete(jobId);
      } else {
        // Apply exponential backoff for retries
        const interval = pollingIntervalsRef.current.get(jobId);
        if (interval) {
          clearInterval(interval);
          pollingIntervalsRef.current.delete(jobId);
        }

        // Calculate backoff delay: base interval * 2^errorCount, max 60 seconds
        const backoffDelay = Math.min(pollInterval * Math.pow(2, newErrorCount), 60000);

        // Restart polling with backoff delay
        const backoffTimeout = setTimeout(() => {
          const newInterval = setInterval(() => pollJob(jobId), pollInterval);
          pollingIntervalsRef.current.set(jobId, newInterval);
        }, backoffDelay);

        pollingIntervalsRef.current.set(jobId, backoffTimeout as any);
      }
    }
  };

  // Effect to manage polling intervals
  useEffect(() => {
    // Start polling for active jobs that need it
    activeJobs.forEach(jobId => {
      const job = jobs[jobId];
      const isAlreadyPolling = pollingIntervalsRef.current.has(jobId);

      if (
        job &&
        !isAlreadyPolling &&
        (job.status === 'queued' || job.status === 'active' || job.status === 'processing')
      ) {
        // Initial poll
        pollJob(jobId);

        // Set up interval for subsequent polls
        const interval = setInterval(() => {
          pollJob(jobId);
        }, pollInterval);
        pollingIntervalsRef.current.set(jobId, interval);

        // Log interval ID for debugging
      } else if (isAlreadyPolling) {
      } else if (!job) {
      }
    });

    // Stop polling for jobs that are no longer active
    pollingIntervalsRef.current.forEach((interval, jobId) => {
      if (!activeJobs.includes(jobId)) {
        clearInterval(interval);
        pollingIntervalsRef.current.delete(jobId);
      }
    });
  }, [activeJobs.join(','), pollInterval, managerId]); // Use stable string representation of activeJobs

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      pollingIntervalsRef.current.forEach(interval => clearInterval(interval));
      pollingIntervalsRef.current.clear();
    };
  }, [managerId]);
};
