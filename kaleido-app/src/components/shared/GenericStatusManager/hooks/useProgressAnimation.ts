import { useEffect, useRef } from 'react';
import { StatusJob } from '../types';

interface UseProgressAnimationProps {
  jobs: Record<string, StatusJob>;
  activeJobs: string[];
  onUpdateJob: (jobId: string, updates: Partial<StatusJob>) => void;
  animationInterval?: number; // milliseconds
  incrementAmount?: number; // percentage
}

export const useProgressAnimation = ({
  jobs,
  activeJobs,
  onUpdateJob,
  animationInterval = 3000, // 3 seconds
  incrementAmount = 5, // 5% each time
}: UseProgressAnimationProps) => {
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    // Clear any existing interval
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }

    // Only start animation if there are active jobs
    const activeProcessingJobs = activeJobs.filter(jobId => {
      const job = jobs[jobId];
      return (
        job && (job.status === 'processing' || job.status === 'active' || job.status === 'queued')
      );
    });

    if (activeProcessingJobs.length === 0) {
      return;
    }

    // Set up interval to increment progress
    intervalRef.current = setInterval(() => {
      activeProcessingJobs.forEach(jobId => {
        const job = jobs[jobId];
        if (!job) return;

        // Don't animate if job is no longer processing
        if (job.status !== 'processing' && job.status !== 'active' && job.status !== 'queued') {
          return;
        }

        // Calculate new progress
        const currentProgress = job.progress || 0;
        let newProgress = currentProgress + incrementAmount;

        // Cap progress at 95% to avoid showing 100% before actual completion
        if (newProgress > 95) {
          newProgress = 95;
        }

        // Only update if progress would actually change
        if (newProgress !== currentProgress) {
          onUpdateJob(jobId, {
            progress: newProgress,
            // Optionally update message to show progress
            message: job.message || `Processing... ${Math.round(newProgress)}%`,
          });
        }
      });
    }, animationInterval);

    // Cleanup
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [activeJobs, jobs, onUpdateJob, animationInterval, incrementAmount]);
};
