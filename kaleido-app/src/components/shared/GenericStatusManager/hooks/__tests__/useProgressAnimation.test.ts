import { renderHook } from '@testing-library/react';
import { act } from '@testing-library/react';
import { useProgressAnimation } from '../useProgressAnimation';
import { StatusJob } from '../../types';

describe('useProgressAnimation', () => {
  const mockOnUpdateJob = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    jest.useFakeTimers();
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  it('should not start animation when there are no active jobs', () => {
    const jobs: Record<string, StatusJob> = {
      job1: { id: 'job1', status: 'completed', progress: 100 },
    };

    renderHook(() =>
      useProgressAnimation({
        jobs,
        activeJobs: ['job1'],
        onUpdateJob: mockOnUpdateJob,
      })
    );

    act(() => {
      jest.advanceTimersByTime(3000);
    });

    expect(mockOnUpdateJob).not.toHaveBeenCalled();
  });

  it('should increment progress for active jobs every 3 seconds', () => {
    const jobs: Record<string, StatusJob> = {
      job1: { id: 'job1', status: 'processing', progress: 10 },
      job2: { id: 'job2', status: 'active', progress: 20 },
    };

    renderHook(() =>
      useProgressAnimation({
        jobs,
        activeJobs: ['job1', 'job2'],
        onUpdateJob: mockOnUpdateJob,
      })
    );

    // First increment at 3 seconds
    act(() => {
      jest.advanceTimersByTime(3000);
    });

    expect(mockOnUpdateJob).toHaveBeenCalledWith('job1', {
      progress: 15,
      message: 'Processing... 15%',
    });
    expect(mockOnUpdateJob).toHaveBeenCalledWith('job2', {
      progress: 25,
      message: 'Processing... 25%',
    });

    // Second increment at 6 seconds
    act(() => {
      jest.advanceTimersByTime(3000);
    });

    expect(mockOnUpdateJob).toHaveBeenCalledTimes(4);
  });

  it('should cap progress at 95%', () => {
    let currentJobs: Record<string, StatusJob> = {
      job1: { id: 'job1', status: 'processing', progress: 93 },
    };

    const { rerender } = renderHook(
      ({ jobs }) =>
        useProgressAnimation({
          jobs,
          activeJobs: ['job1'],
          onUpdateJob: mockOnUpdateJob,
        }),
      {
        initialProps: { jobs: currentJobs },
      }
    );

    act(() => {
      jest.advanceTimersByTime(3000);
    });

    expect(mockOnUpdateJob).toHaveBeenCalledWith('job1', {
      progress: 95,
      message: 'Processing... 95%',
    });

    // Update the jobs to reflect the new progress
    currentJobs = {
      job1: { id: 'job1', status: 'processing', progress: 95 },
    };
    rerender({ jobs: currentJobs });

    // Should not increment beyond 95%
    mockOnUpdateJob.mockClear();
    act(() => {
      jest.advanceTimersByTime(3000);
    });

    expect(mockOnUpdateJob).not.toHaveBeenCalled();
  });

  it('should use custom animation interval and increment amount', () => {
    const jobs: Record<string, StatusJob> = {
      job1: { id: 'job1', status: 'processing', progress: 0 },
    };

    renderHook(() =>
      useProgressAnimation({
        jobs,
        activeJobs: ['job1'],
        onUpdateJob: mockOnUpdateJob,
        animationInterval: 1000, // 1 second
        incrementAmount: 10, // 10% each time
      })
    );

    act(() => {
      jest.advanceTimersByTime(1000);
    });

    expect(mockOnUpdateJob).toHaveBeenCalledWith('job1', {
      progress: 10,
      message: 'Processing... 10%',
    });
  });

  it('should preserve existing message if present', () => {
    const jobs: Record<string, StatusJob> = {
      job1: {
        id: 'job1',
        status: 'processing',
        progress: 10,
        message: 'Custom message',
      },
    };

    renderHook(() =>
      useProgressAnimation({
        jobs,
        activeJobs: ['job1'],
        onUpdateJob: mockOnUpdateJob,
      })
    );

    act(() => {
      jest.advanceTimersByTime(3000);
    });

    expect(mockOnUpdateJob).toHaveBeenCalledWith('job1', {
      progress: 15,
      message: 'Custom message',
    });
  });

  it('should handle queued jobs', () => {
    const jobs: Record<string, StatusJob> = {
      job1: { id: 'job1', status: 'queued', progress: 0 },
    };

    renderHook(() =>
      useProgressAnimation({
        jobs,
        activeJobs: ['job1'],
        onUpdateJob: mockOnUpdateJob,
      })
    );

    act(() => {
      jest.advanceTimersByTime(3000);
    });

    expect(mockOnUpdateJob).toHaveBeenCalledWith('job1', {
      progress: 5,
      message: 'Processing... 5%',
    });
  });

  it('should stop animation when job status changes to non-processing', () => {
    const jobs: Record<string, StatusJob> = {
      job1: { id: 'job1', status: 'processing', progress: 10 },
    };

    const { rerender } = renderHook(
      ({ jobs, activeJobs }) =>
        useProgressAnimation({
          jobs,
          activeJobs,
          onUpdateJob: mockOnUpdateJob,
        }),
      {
        initialProps: { jobs, activeJobs: ['job1'] },
      }
    );

    // First increment
    act(() => {
      jest.advanceTimersByTime(3000);
    });

    expect(mockOnUpdateJob).toHaveBeenCalledTimes(1);

    // Change job status to completed
    const updatedJobs = {
      job1: { id: 'job1', status: 'completed' as const, progress: 100 },
    };

    rerender({ jobs: updatedJobs, activeJobs: ['job1'] });

    // Should not increment anymore
    mockOnUpdateJob.mockClear();
    act(() => {
      jest.advanceTimersByTime(3000);
    });

    expect(mockOnUpdateJob).not.toHaveBeenCalled();
  });

  it('should clean up interval on unmount', () => {
    const jobs: Record<string, StatusJob> = {
      job1: { id: 'job1', status: 'processing', progress: 10 },
    };

    const { unmount } = renderHook(() =>
      useProgressAnimation({
        jobs,
        activeJobs: ['job1'],
        onUpdateJob: mockOnUpdateJob,
      })
    );

    unmount();

    // Should not call update after unmount
    act(() => {
      jest.advanceTimersByTime(3000);
    });

    expect(mockOnUpdateJob).not.toHaveBeenCalled();
  });

  it('should handle missing jobs gracefully', () => {
    const jobs: Record<string, StatusJob> = {};

    renderHook(() =>
      useProgressAnimation({
        jobs,
        activeJobs: ['nonexistent'],
        onUpdateJob: mockOnUpdateJob,
      })
    );

    act(() => {
      jest.advanceTimersByTime(3000);
    });

    expect(mockOnUpdateJob).not.toHaveBeenCalled();
  });
});
