'use client';

import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { motion } from 'framer-motion';
import {
  ArrowLeft,
  Download,
  Share2,
  Trophy,
  AlertCircle,
  CheckCircle,
  XCircle,
  BarChart3,
  Users,
} from 'lucide-react';
import { comparisonService } from '@/services/comparison.service';
import { ComparisonResult } from '@/types/comparison.types';
import { showToast } from '@/components/Toaster';

interface ComparisonResultsProps {
  jobId: string;
  comparisonId: string;
}

export const ComparisonResults: React.FC<ComparisonResultsProps> = ({ jobId, comparisonId }) => {
  const router = useRouter();
  const [comparison, setComparison] = useState<ComparisonResult | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadComparison();
  }, [comparisonId]);

  const loadComparison = async () => {
    try {
      setLoading(true);
      const result = await comparisonService.getComparison(comparisonId);
      setComparison(result);
    } catch (err) {
      console.error('Error loading comparison:', err);
      setError('Failed to load comparison results');
      showToast({
        message: 'Failed to load comparison results',
        isSuccess: false,
      });
    } finally {
      setLoading(false);
    }
  };

  const handleExport = async () => {
    try {
      const report = await comparisonService.generateReport(comparisonId);
      // TODO: Implement actual export functionality
      showToast({
        message: 'Report generated successfully',
        isSuccess: true,
      });
    } catch (err) {
      showToast({
        message: 'Failed to generate report',
        isSuccess: false,
      });
    }
  };

  const handleShare = () => {
    // TODO: Implement share functionality
    showToast({
      message: 'Share feature coming soon',
      type: 'info',
    });
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full animate-spin mx-auto mb-4" />
          <p className="text-lg font-medium">Loading comparison results...</p>
        </div>
      </div>
    );
  }

  if (error || !comparison) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <AlertCircle className="w-16 h-16 text-red-500 mx-auto mb-4" />
          <p className="text-lg font-medium text-red-500">{error || 'No comparison data found'}</p>
          <button
            onClick={() => router.back()}
            className="mt-4 px-4 py-2 bg-primary hover:bg-primary/80 text-primary-foreground rounded-lg transition-colors"
          >
            Go Back
          </button>
        </div>
      </div>
    );
  }

  const results = comparison.comparisonResults;
  const candidateAnalysis = results?.candidateAnalysis || {};
  const recommendations = results?.recommendations;
  const criticalConsiderations = results?.criticalConsiderations || [];

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="sticky top-0 z-30 bg-background/95 backdrop-blur-sm border-b border-border">
        <div className="max-w-7xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <button
                onClick={() => router.back()}
                className="p-2 hover:bg-white/10 rounded-lg transition-colors"
              >
                <ArrowLeft className="w-5 h-5" />
              </button>
              <div>
                <h1 className="text-2xl font-bold">{comparison.comparisonTitle}</h1>
                <p className="text-sm text-muted-foreground">
                  Generated on {new Date(comparison.createdAt || '').toLocaleDateString()}
                </p>
              </div>
            </div>

            <div className="flex items-center gap-2">
              <button
                onClick={handleShare}
                className="px-4 py-2 bg-white/10 hover:bg-white/15 border border-white/20 rounded-lg transition-colors flex items-center gap-2"
              >
                <Share2 className="w-4 h-4" />
                Share
              </button>
              <button
                onClick={handleExport}
                className="px-4 py-2 bg-primary hover:bg-primary/80 text-primary-foreground rounded-lg transition-colors flex items-center gap-2"
              >
                <Download className="w-4 h-4" />
                Export
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-7xl mx-auto px-4 py-8">
        {/* Executive Summary */}
        {results?.executiveSummary && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-card border border-border rounded-lg p-6 mb-8"
          >
            <h2 className="text-xl font-semibold mb-4 flex items-center gap-2">
              <BarChart3 className="w-5 h-5 text-purple-400" />
              Executive Summary
            </h2>
            <p className="text-muted-foreground leading-relaxed">{results.executiveSummary}</p>
          </motion.div>
        )}

        {/* Top Recommendation */}
        {recommendations?.topChoice && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="bg-gradient-to-br from-purple-500/10 to-pink-500/10 border border-purple-400/30 rounded-lg p-6 mb-8"
          >
            <h2 className="text-xl font-semibold mb-4 flex items-center gap-2">
              <Trophy className="w-5 h-5 text-yellow-500" />
              Top Recommendation
            </h2>
            <div className="space-y-3">
              <p className="text-lg font-medium">
                Candidate: {recommendations.topChoice.candidateId}
              </p>
              <p className="text-muted-foreground leading-relaxed">
                {recommendations.topChoice.reasoning}
              </p>
            </div>
          </motion.div>
        )}

        {/* Candidate Analysis Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6 mb-8">
          {Object.entries(candidateAnalysis).map(
            ([candidateId, analysis]: [string, any], index) => (
              <motion.div
                key={candidateId}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 + index * 0.1 }}
                className="bg-card border border-border rounded-lg p-6"
              >
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold">{candidateId}</h3>
                  <div
                    className={`
                  px-3 py-1 rounded-full text-sm font-medium
                  ${
                    analysis.overallRank === 1
                      ? 'bg-green-500/20 text-green-400 border border-green-400/30'
                      : 'bg-gray-500/20 text-gray-400 border border-gray-400/30'
                  }
                `}
                  >
                    Rank #{analysis.overallRank}
                  </div>
                </div>

                {/* Strengths */}
                {analysis.strengths && analysis.strengths.length > 0 && (
                  <div className="mb-4">
                    <h4 className="text-sm font-medium text-green-400 mb-2 flex items-center gap-1">
                      <CheckCircle className="w-4 h-4" />
                      Strengths
                    </h4>
                    <ul className="space-y-1">
                      {analysis.strengths.slice(0, 3).map((strength: string, idx: number) => (
                        <li key={idx} className="text-sm text-muted-foreground">
                          • {strength}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}

                {/* Weaknesses */}
                {analysis.weaknesses && analysis.weaknesses.length > 0 && (
                  <div className="mb-4">
                    <h4 className="text-sm font-medium text-orange-400 mb-2 flex items-center gap-1">
                      <AlertCircle className="w-4 h-4" />
                      Areas of Concern
                    </h4>
                    <ul className="space-y-1">
                      {analysis.weaknesses.slice(0, 3).map((weakness: string, idx: number) => (
                        <li key={idx} className="text-sm text-muted-foreground">
                          • {weakness}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}

                {/* Key Differentiator */}
                {analysis.keyDifferentiator && (
                  <div className="pt-4 border-t border-border">
                    <p className="text-sm text-purple-400 font-medium">Key Differentiator:</p>
                    <p className="text-sm text-muted-foreground mt-1">
                      {analysis.keyDifferentiator}
                    </p>
                  </div>
                )}
              </motion.div>
            )
          )}
        </div>

        {/* Alternative Scenarios */}
        {recommendations?.alternativeScenarios &&
          recommendations.alternativeScenarios.length > 0 && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5 }}
              className="bg-card border border-border rounded-lg p-6 mb-8"
            >
              <h2 className="text-xl font-semibold mb-4 flex items-center gap-2">
                <Users className="w-5 h-5 text-blue-400" />
                Alternative Scenarios
              </h2>
              <div className="space-y-4">
                {recommendations.alternativeScenarios.map((scenario: any, index: number) => (
                  <div key={index} className="p-4 bg-white/5 rounded-lg">
                    <p className="font-medium text-blue-400 mb-2">{scenario.scenario}</p>
                    <p className="text-sm text-muted-foreground">
                      <span className="font-medium">Recommended: </span>
                      {scenario.recommendedCandidate}
                    </p>
                    {scenario.reasoning && (
                      <p className="text-sm text-muted-foreground mt-1">{scenario.reasoning}</p>
                    )}
                  </div>
                ))}
              </div>
            </motion.div>
          )}

        {/* Critical Considerations */}
        {criticalConsiderations.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6 }}
            className="bg-card border border-border rounded-lg p-6"
          >
            <h2 className="text-xl font-semibold mb-4 flex items-center gap-2">
              <AlertCircle className="w-5 h-5 text-yellow-500" />
              Critical Considerations
            </h2>
            <ul className="space-y-2">
              {criticalConsiderations.map((consideration: string, index: number) => (
                <li key={index} className="flex items-start gap-2">
                  <span className="text-yellow-500 mt-1">•</span>
                  <span className="text-muted-foreground">{consideration}</span>
                </li>
              ))}
            </ul>
          </motion.div>
        )}
      </div>
    </div>
  );
};
