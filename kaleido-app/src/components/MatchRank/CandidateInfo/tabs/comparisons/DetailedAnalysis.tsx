'use client';

import { AnimatePresence, motion } from 'framer-motion';
import {
  Briefcase,
  CheckCircle,
  ChevronDown,
  ChevronUp,
  Eye,
  GitCompare,
  MessageSquare,
  Target,
  UserCheck,
  Users,
} from 'lucide-react';
import React, { useState } from 'react';
import { <PERSON><PERSON>hart, Pie, Cell, ResponsiveContainer } from 'recharts';

interface DetailedAnalysisProps {
  candidateAnalysis?: any;
  metadata?: any;
  headToHeadComparisons?: any[];
  recommendations?: any;
}

const COLORS = ['#3b82f6', '#06b6d4', '#8b5cf6', '#ec4899', '#f59e0b'];

const GRADIENT_COLORS = [
  'from-blue-600/20 to-blue-800/20',
  'from-cyan-600/20 to-teal-800/20',
  'from-purple-600/20 to-purple-800/20',
  'from-pink-600/20 to-pink-800/20',
  'from-orange-600/20 to-orange-800/20',
];

export const DetailedAnalysis: React.FC<DetailedAnalysisProps> = ({
  candidateAnalysis,
  metadata,
  headToHeadComparisons,
  recommendations,
}) => {
  const [expandedSection, setExpandedSection] = useState<string | null>(null);

  if (!candidateAnalysis) {
    return (
      <div className="text-center py-12">
        <Users className="w-16 h-16 mx-auto mb-4 text-gray-400" />
        <p className="text-white/60">No analysis data available</p>
      </div>
    );
  }

  // Get real candidate names and map to metadata
  const getCandidateInfo = (key: string) => {
    const analysis = candidateAnalysis[key] || {};

    // Get name from metadata
    let name = key;
    let role = 'Candidate';

    if (metadata?.candidateNames) {
      // Try to match by different methods
      const candidateNumber = key.match(/candidate(\d+)/)?.[1];

      if (candidateNumber) {
        const index = parseInt(candidateNumber) - 1;
        const candidateInfo = metadata.candidateNames[index];
        if (candidateInfo) {
          name = candidateInfo.name;
        }
      }
    }

    // Infer role from job title in metadata
    if (metadata?.jobTitle) {
      role = metadata.jobTitle;
    }

    return { name, role, analysis };
  };

  // Prepare real data for cards - using actual scores from the data
  const candidatesData = Object.keys(candidateAnalysis).map((key, index) => {
    const { name, role, analysis } = getCandidateInfo(key);
    const scores = analysis.scores || {};

    // Calculate actual overall score from the 5 metrics
    const overallScore =
      scores.skills &&
      scores.experience &&
      scores.leadership &&
      scores.culturalFit &&
      scores.availability
        ? Math.round(
            (scores.skills +
              scores.experience +
              scores.leadership +
              scores.culturalFit +
              scores.availability) /
              5
          )
        : 0;

    return {
      name,
      role,
      key,
      overallScore,
      experience: scores.experience || 0,
      skills: scores.skills || 0,
      leadership: scores.leadership || 0,
      culturalFit: scores.culturalFit || 0,
      availability: scores.availability || 0,
      rank: analysis.overallRank || index + 1,
      strengths: analysis.strengths || [],
      weaknesses: analysis.weaknesses || [],
      keyDifferentiator: analysis.keyDifferentiator || '',
      uniqueAdvantages: analysis.uniqueAdvantages || [],
      color: COLORS[index % COLORS.length],
      gradient: GRADIENT_COLORS[index % GRADIENT_COLORS.length],
    };
  });

  // Sort by rank
  candidatesData.sort((a, b) => a.rank - b.rank);

  const toggleSection = (section: string) => {
    setExpandedSection(expandedSection === section ? null : section);
  };

  // Get icon for metric
  const getMetricIcon = (metric: string) => {
    if (metric === 'Experience') return <Briefcase className="w-4 h-4" />;
    if (metric === 'Skills' || metric === 'Skills Match')
      return <CheckCircle className="w-4 h-4" />;
    if (metric === 'Leadership') return <Users className="w-4 h-4" />;
    if (metric === 'Culture Fit') return <UserCheck className="w-4 h-4" />;
    if (metric === 'Availability') return <Target className="w-4 h-4" />;
    return <Target className="w-4 h-4" />;
  };

  // Get top metrics for each candidate - using real scores
  const getTopMetrics = (candidate: any) => {
    // Show the top 3 scoring metrics for each candidate
    const allMetrics = [
      { name: 'Skills', value: `${candidate.skills}%`, score: candidate.skills },
      { name: 'Experience', value: `${candidate.experience}%`, score: candidate.experience },
      { name: 'Leadership', value: `${candidate.leadership}%`, score: candidate.leadership },
      { name: 'Culture Fit', value: `${candidate.culturalFit}%`, score: candidate.culturalFit },
      { name: 'Availability', value: `${candidate.availability}%`, score: candidate.availability },
    ];

    // Sort by score and take top 3
    return allMetrics.sort((a, b) => b.score - a.score).slice(0, 3);
  };

  // Prepare donut chart data
  const createDonutData = (score: number) => [
    { name: 'Score', value: score },
    { name: 'Remaining', value: 100 - score },
  ];

  return (
    <div className="space-y-3">
      {/* Candidate Cards Section - Compact */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-3">
        {candidatesData.map((candidate, index) => {
          const metrics = getTopMetrics(candidate);

          return (
            <motion.div
              key={candidate.name}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.05 }}
              className={`relative bg-gradient-to-br ${candidate.gradient} backdrop-blur-sm rounded-lg border border-white/10 p-4 overflow-hidden`}
            >
              {/* Background decoration */}
              <div className="absolute top-0 right-0 w-24 h-24 bg-white/5 rounded-full blur-2xl" />

              {/* Content */}
              <div className="relative z-10 space-y-3">
                {/* Header with Donut Chart */}
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="text-base font-bold text-white">{candidate.name}</h3>
                    <p className="text-xs text-white/70">{candidate.role}</p>
                  </div>

                  {/* Donut Chart */}
                  <div className="relative w-16 h-16">
                    <ResponsiveContainer width="100%" height="100%">
                      <PieChart>
                        <Pie
                          data={createDonutData(candidate.overallScore)}
                          cx="50%"
                          cy="50%"
                          innerRadius={18}
                          outerRadius={28}
                          startAngle={90}
                          endAngle={-270}
                          dataKey="value"
                          stroke="none"
                        >
                          <Cell fill={candidate.color} />
                          <Cell fill="rgba(255, 255, 255, 0.1)" />
                        </Pie>
                      </PieChart>
                    </ResponsiveContainer>
                    <div className="absolute inset-0 flex items-center justify-center">
                      <span className="text-xs font-bold text-white">
                        {candidate.overallScore}%
                      </span>
                    </div>
                  </div>
                </div>

                {/* Compact Metrics */}
                <div className="space-y-1.5">
                  {metrics.map((metric, idx) => (
                    <div key={metric.name} className="flex items-center justify-between">
                      <div className="flex items-center gap-1.5">
                        <div className="text-white/60 scale-75">{getMetricIcon(metric.name)}</div>
                        <span className="text-xs text-white/70">{metric.name}</span>
                      </div>
                      <span className="text-xs font-semibold text-white">{metric.value}</span>
                    </div>
                  ))}
                </div>
              </div>
            </motion.div>
          );
        })}
      </div>

      {/* Key Differences Section - Card Style */}
      {headToHeadComparisons && headToHeadComparisons.length > 0 && (
        <div>
          <h4 className="text-sm font-semibold mb-2 text-white flex items-center gap-2">
            <GitCompare className="w-4 h-4 text-purple-400" />
            Key Differences
          </h4>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
            {headToHeadComparisons.map((comparison, index) => {
              // Determine colors based on candidates
              const candidate1Index = candidatesData.findIndex(
                c => c.name === comparison.candidate1
              );
              const candidate2Index = candidatesData.findIndex(
                c => c.name === comparison.candidate2
              );
              const color1 =
                candidate1Index >= 0 ? candidatesData[candidate1Index].color : COLORS[0];
              const color2 =
                candidate2Index >= 0 ? candidatesData[candidate2Index].color : COLORS[1];

              return (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, scale: 0.95 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 0.3 + index * 0.05 }}
                  className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm rounded-lg border border-white/10 p-3 space-y-2"
                >
                  {/* Candidates comparison header */}
                  <div className="flex items-center justify-center gap-2">
                    <div className="flex items-center gap-1">
                      <div
                        className="w-2.5 h-2.5 rounded-full"
                        style={{ backgroundColor: color1 }}
                      />
                      <span className="text-xs font-medium text-white">
                        {comparison.candidate1}
                      </span>
                    </div>
                    <span className="text-purple-400 text-xs font-bold">vs</span>
                    <div className="flex items-center gap-1">
                      <div
                        className="w-2.5 h-2.5 rounded-full"
                        style={{ backgroundColor: color2 }}
                      />
                      <span className="text-xs font-medium text-white">
                        {comparison.candidate2}
                      </span>
                    </div>
                  </div>

                  {/* Key difference */}
                  <div className="bg-purple-500/10 rounded-md p-2 border border-purple-400/20">
                    <p className="text-xs text-white/80 leading-relaxed">
                      {comparison.keyDifference}
                    </p>
                  </div>

                  {/* Recommendation */}
                  <div className="flex items-start gap-1.5">
                    <Target className="w-3.5 h-3.5 text-purple-400 flex-shrink-0 mt-0.5" />
                    <p className="text-xs text-purple-300">{comparison.recommendation}</p>
                  </div>
                </motion.div>
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
};
