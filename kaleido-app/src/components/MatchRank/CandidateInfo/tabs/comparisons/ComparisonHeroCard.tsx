'use client';

import React from 'react';
import { motion } from 'framer-motion';
import {
  GitCompare,
  Calendar,
  Users,
  BarChart3,
  Target,
  TrendingUp,
  ArrowRight,
} from 'lucide-react';
import { format } from 'date-fns';

interface ComparisonHeroCardProps {
  comparison: {
    id: string;
    comparisonTitle: string;
    comparisonType: string;
    status: 'pending' | 'completed' | 'failed';
    createdAt: string;
    candidateIds: string[];
    comparisonResults?: {
      summary: string;
      executiveSummary?: string;
      detailedComparison?: {
        [candidateId: string]: any;
      };
      candidateAnalysis?: {
        [candidateId: string]: any;
      };
      recommendations?: any;
      tradeOffs?: Array<{
        candidateA: string;
        candidateB: string;
        comparison: string;
      }>;
      headToHeadComparisons?: Array<{
        candidate1: string;
        candidate2: string;
        keyDifference: string;
        recommendation: string;
      }>;
      criticalConsiderations?: string[];
    };
    visualizationData?: any;
  };
  onViewDetails: () => void;
}

const comparisonTypeConfig = {
  quick_overview: {
    icon: BarChart3,
    color: 'from-blue-500 to-cyan-500',
    bgColor: 'from-blue-500/10 to-cyan-500/10',
  },
  skills_deep_dive: {
    icon: Target,
    color: 'from-purple-500 to-pink-500',
    bgColor: 'from-purple-500/10 to-pink-500/10',
  },
  culture_fit: {
    icon: Users,
    color: 'from-green-500 to-emerald-500',
    bgColor: 'from-green-500/10 to-emerald-500/10',
  },
  experience_comparison: {
    icon: TrendingUp,
    color: 'from-orange-500 to-red-500',
    bgColor: 'from-orange-500/10 to-red-500/10',
  },
  leadership_potential: {
    icon: GitCompare,
    color: 'from-indigo-500 to-purple-500',
    bgColor: 'from-indigo-500/10 to-purple-500/10',
  },
  custom: {
    icon: GitCompare,
    color: 'from-gray-500 to-gray-600',
    bgColor: 'from-gray-500/10 to-gray-600/10',
  },
};

export const ComparisonHeroCard: React.FC<ComparisonHeroCardProps> = ({
  comparison,
  onViewDetails,
}) => {
  const config =
    comparisonTypeConfig[comparison.comparisonType as keyof typeof comparisonTypeConfig] ||
    comparisonTypeConfig.custom;
  const Icon = config.icon;

  // Extract key stats from the comparison result
  const stats = comparison.visualizationData || {
    totalCandidates: comparison.candidateIds.length,
    keyInsights: comparison.comparisonResults?.criticalConsiderations?.length || 3,
    recommendations: comparison.comparisonResults?.headToHeadComparisons?.length || 2,
  };

  return (
    <motion.div
      className="relative w-full overflow-hidden rounded-2xl"
      whileHover={{ scale: 1.02 }}
      transition={{ duration: 0.2 }}
    >
      {/* Background gradient */}
      <div className={`absolute inset-0 bg-gradient-to-br ${config.bgColor}`} />

      {/* Decorative elements */}
      <div className="absolute top-0 right-0 w-96 h-96 bg-gradient-to-br from-white/5 to-transparent rounded-full blur-3xl transform translate-x-32 -translate-y-32" />
      <div className="absolute bottom-0 left-0 w-64 h-64 bg-gradient-to-tr from-white/5 to-transparent rounded-full blur-2xl transform -translate-x-16 translate-y-16" />

      {/* Content */}
      <div className="relative p-8 lg:p-10">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Left side - Info */}
          <div className="space-y-6">
            {/* Header */}
            <div className="flex items-start gap-4">
              <div className={`p-3 rounded-xl bg-gradient-to-br ${config.color} shadow-lg`}>
                <Icon className="w-6 h-6 text-white" />
              </div>
              <div className="flex-1">
                <h2 className="text-2xl font-bold mb-1">{comparison.comparisonTitle}</h2>
                <p className="text-sm text-muted-foreground flex items-center gap-2">
                  <Calendar className="w-4 h-4" />
                  {format(new Date(comparison.createdAt), 'MMM dd, yyyy • h:mm a')}
                </p>
              </div>
            </div>

            {/* Executive Summary */}
            {comparison.comparisonResults?.executiveSummary && (
              <div className="space-y-2">
                <h3 className="text-sm font-semibold text-muted-foreground uppercase tracking-wide">
                  Executive Summary
                </h3>
                <p className="text-lg leading-relaxed line-clamp-3">
                  {comparison.comparisonResults.executiveSummary}
                </p>
              </div>
            )}

            {/* CTA Button */}
            <button
              onClick={onViewDetails}
              className={`inline-flex items-center gap-2 px-6 py-3 rounded-lg bg-gradient-to-r ${config.color} text-white font-medium shadow-lg hover:shadow-xl transition-all duration-200 group`}
            >
              View Full Comparison
              <ArrowRight className="w-4 h-4 group-hover:translate-x-1 transition-transform" />
            </button>
          </div>

          {/* Right side - Stats */}
          <div className="grid grid-cols-2 gap-4">
            {/* Candidates Count */}
            <motion.div
              className="bg-background/50 backdrop-blur-sm rounded-xl p-6 border border-gray-200/20"
              whileHover={{ y: -4 }}
              transition={{ duration: 0.2 }}
            >
              <Users className="w-8 h-8 text-purple-600 mb-3" />
              <div className="text-3xl font-bold mb-1">{stats.totalCandidates}</div>
              <div className="text-sm text-muted-foreground">Candidates Compared</div>
            </motion.div>

            {/* Key Insights */}
            <motion.div
              className="bg-background/50 backdrop-blur-sm rounded-xl p-6 border border-gray-200/20"
              whileHover={{ y: -4 }}
              transition={{ duration: 0.2 }}
            >
              <Target className="w-8 h-8 text-blue-600 mb-3" />
              <div className="text-3xl font-bold mb-1">{stats.keyInsights}</div>
              <div className="text-sm text-muted-foreground">Key Insights</div>
            </motion.div>

            {/* Comparison Type */}
            <motion.div
              className="bg-background/50 backdrop-blur-sm rounded-xl p-6 border border-gray-200/20"
              whileHover={{ y: -4 }}
              transition={{ duration: 0.2 }}
            >
              <BarChart3 className="w-8 h-8 text-green-600 mb-3" />
              <div className="text-lg font-bold mb-1 capitalize">
                {comparison.comparisonType.replace(/_/g, ' ')}
              </div>
              <div className="text-sm text-muted-foreground">Analysis Type</div>
            </motion.div>

            {/* Status */}
            <motion.div
              className="bg-background/50 backdrop-blur-sm rounded-xl p-6 border border-gray-200/20"
              whileHover={{ y: -4 }}
              transition={{ duration: 0.2 }}
            >
              <div
                className={`w-8 h-8 rounded-full mb-3 ${
                  comparison.status === 'completed'
                    ? 'bg-green-500'
                    : comparison.status === 'pending'
                      ? 'bg-yellow-500 animate-pulse'
                      : 'bg-red-500'
                }`}
              />
              <div className="text-lg font-bold mb-1 capitalize">{comparison.status}</div>
              <div className="text-sm text-muted-foreground">Comparison Status</div>
            </motion.div>
          </div>
        </div>
      </div>
    </motion.div>
  );
};
