'use client';

import { motion } from 'framer-motion';
import { BarChart3, Star, TrendingUp, Users } from 'lucide-react';
import React from 'react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  RadialLinearScale,
  PointElement,
  LineElement,
  Filler,
} from 'chart.js';
import { Bar, Doughnut, Radar } from 'react-chartjs-2';
import ChartDataLabels from 'chartjs-plugin-datalabels';

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  RadialLinearScale,
  PointElement,
  LineElement,
  Filler,
  ChartDataLabels
);

interface ComparisonStatsProps {
  data?: any;
  candidateAnalysis?: any;
  metadata?: any;
  headToHeadComparisons?: any[];
}

const COLORS = ['#8b5cf6', '#ec4899', '#06b6d4', '#10b981', '#f59e0b'];

export const ComparisonStats: React.FC<ComparisonStatsProps> = ({
  data,
  candidateAnalysis,
  metadata,
  headToHeadComparisons,
}) => {
  // Check if we have the new data structure from API
  const hasApiChartData = data?.barChartData || data?.radarChartData;

  if (!data || (!candidateAnalysis && !hasApiChartData)) {
    return (
      <div className="text-center py-12">
        <BarChart3 className="w-16 h-16 mx-auto mb-4 text-gray-400" />
        <p className="text-white/60">No visualization data available</p>
      </div>
    );
  }

  // If we have API chart data, use it directly
  if (hasApiChartData) {
    const barChartData = data.barChartData;
    const radarChartData = data.radarChartData;
    const comparisonMatrix = data.comparisonMatrix;

    // Prepare data
    const barData =
      barChartData?.labels?.map((label: string, index: number) => ({
        name: label,
        score: barChartData.datasets[0].data[index],
      })) || [];

    // Sort candidates by score for ranking
    const sortedCandidates = [...barData].sort((a, b) => b.score - a.score);

    return (
      <div className="space-y-6">
        {/* Clean Bar Chart with Visual Indicators */}
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-gray-800/30 rounded-xl p-6 border border-gray-700/30"
        >
          <h4 className="text-lg font-semibold text-white mb-6 flex items-center gap-2">
            <BarChart3 className="w-5 h-5 text-purple-400" />
            Candidate Comparison
          </h4>

          {/* Visual Score Bars */}
          <div className="space-y-4">
            {sortedCandidates.map((candidate, index) => (
              <div key={candidate.name} className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    {index === 0 && (
                      <Star className="w-5 h-5 text-purple-400" fill="currentColor" />
                    )}
                    <span className="font-medium text-white">{candidate.name}</span>
                  </div>
                  <span className="text-lg font-bold text-white">{candidate.score}%</span>
                </div>
                <div className="relative w-full bg-gray-700/50 rounded-full h-8 overflow-hidden">
                  <motion.div
                    initial={{ width: 0 }}
                    animate={{ width: `${candidate.score}%` }}
                    transition={{ duration: 0.8, delay: index * 0.1 }}
                    className="absolute h-full rounded-full bg-gradient-to-r"
                    style={{
                      backgroundImage: `linear-gradient(to right, ${COLORS[index % COLORS.length]}CC, ${COLORS[index % COLORS.length]})`,
                    }}
                  />
                  <div className="absolute inset-0 flex items-center px-3">
                    <span className="text-sm font-medium text-white/90">{candidate.score}%</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </motion.div>

        {/* Skills Radar Chart */}
        {radarChartData && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="bg-gray-800/30 rounded-xl p-6 border border-gray-700/30"
          >
            <h4 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
              <TrendingUp className="w-5 h-5 text-purple-400" />
              Skills Comparison
            </h4>
            <div className="h-80">
              <Radar
                data={{
                  labels: radarChartData.labels,
                  datasets: radarChartData.datasets.map((dataset: any, index: number) => ({
                    label: dataset.label,
                    data: dataset.data,
                    backgroundColor: COLORS[index % COLORS.length] + '33',
                    borderColor: COLORS[index % COLORS.length],
                    borderWidth: 2,
                    pointBackgroundColor: COLORS[index % COLORS.length],
                    pointBorderColor: '#fff',
                    pointHoverBackgroundColor: '#fff',
                    pointHoverBorderColor: COLORS[index % COLORS.length],
                  })),
                }}
                options={{
                  responsive: true,
                  maintainAspectRatio: false,
                  plugins: {
                    legend: {
                      position: 'bottom',
                      labels: {
                        color: '#fff',
                        padding: 20,
                        font: {
                          size: 12,
                        },
                        usePointStyle: true,
                      },
                    },
                    tooltip: {
                      backgroundColor: '#1f2937',
                      titleColor: '#fff',
                      bodyColor: '#fff',
                      padding: 12,
                      cornerRadius: 8,
                      displayColors: false,
                      callbacks: {
                        label: context => {
                          return `${context.dataset.label}: ${context.parsed.r}%`;
                        },
                      },
                    },
                    datalabels: {
                      display: false,
                    },
                  },
                  scales: {
                    r: {
                      beginAtZero: true,
                      max: 100,
                      ticks: {
                        color: '#9ca3af',
                        backdropColor: 'transparent',
                        stepSize: 20,
                        display: false,
                      },
                      grid: {
                        color: 'rgba(255, 255, 255, 0.1)',
                      },
                      angleLines: {
                        color: 'rgba(255, 255, 255, 0.1)',
                      },
                      pointLabels: {
                        color: '#fff',
                        font: {
                          size: 11,
                        },
                      },
                    },
                  },
                }}
              />
            </div>
          </motion.div>
        )}

        {/* Simplified Comparison Table */}
        {comparisonMatrix && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="bg-gray-800/30 rounded-xl p-6 border border-gray-700/30"
          >
            <h4 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
              <Users className="w-5 h-5 text-purple-400" />
              Key Insights
            </h4>
            <div className="space-y-3">
              {sortedCandidates.map((candidate, index) => {
                const row = comparisonMatrix.rows.find((r: any) => r[0] === candidate.name);
                if (!row) return null;

                return (
                  <div
                    key={candidate.name}
                    className="p-4 bg-gray-900/50 rounded-lg border border-gray-700/50"
                  >
                    <div className="flex items-center justify-between mb-2">
                      <h5 className="font-medium text-white flex items-center gap-2">
                        {index === 0 && (
                          <Star className="w-4 h-4 text-purple-400" fill="currentColor" />
                        )}
                        {candidate.name}
                      </h5>
                      <span className="text-sm font-bold text-purple-400">{candidate.score}%</span>
                    </div>
                    <p className="text-sm text-gray-400 mb-2">{row[4]}</p>
                    <div className="grid grid-cols-2 gap-2 text-xs">
                      <div>
                        <span className="text-green-400 font-medium">Strengths:</span>
                        <p className="text-gray-400 mt-1">{row[2]}</p>
                      </div>
                      <div>
                        <span className="text-orange-400 font-medium">Considerations:</span>
                        <p className="text-gray-400 mt-1">{row[3]}</p>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </motion.div>
        )}
      </div>
    );
  }

  // Original logic for old data structure
  const getCandidateInfo = (key: string) => {
    const candidateKey = key.toLowerCase().replace('candidate', '');
    const analysis = candidateAnalysis[key] || {};

    let name = key;
    if (metadata?.candidateNames) {
      const found = metadata.candidateNames.find(
        (c: any) =>
          c.name === analysis.name ||
          key.includes(c.name) ||
          c.name.toLowerCase().includes(candidateKey)
      );
      if (found) name = found.name;
    }

    return { name, analysis };
  };

  // Prepare real data for charts
  const candidatesData = Object.keys(candidateAnalysis).map(key => {
    const { name, analysis } = getCandidateInfo(key);
    return {
      name,
      key,
      overallScore: analysis.scores
        ? Math.round(
            (analysis.scores.skills +
              analysis.scores.experience +
              analysis.scores.leadership +
              analysis.scores.culturalFit +
              analysis.scores.availability) /
              5
          )
        : 0,
      ...analysis.scores,
    };
  });

  candidatesData.sort((a, b) => b.overallScore - a.overallScore);

  return (
    <div className="space-y-6">
      {/* Overall Scores Visual Bar */}
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-gray-800/30 rounded-xl p-6 border border-gray-700/30"
      >
        <h4 className="text-lg font-semibold text-white mb-6 flex items-center gap-2">
          <BarChart3 className="w-5 h-5 text-purple-400" />
          Candidate Comparison
        </h4>

        <div className="space-y-4">
          {candidatesData.map((candidate, index) => (
            <div key={candidate.name} className="space-y-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  {index === 0 && <Star className="w-5 h-5 text-purple-400" fill="currentColor" />}
                  <span className="font-medium text-white">{candidate.name}</span>
                </div>
                <span className="text-lg font-bold text-white">{candidate.overallScore}%</span>
              </div>
              <div className="relative w-full bg-gray-700/50 rounded-full h-8 overflow-hidden">
                <motion.div
                  initial={{ width: 0 }}
                  animate={{ width: `${candidate.overallScore}%` }}
                  transition={{ duration: 0.8, delay: index * 0.1 }}
                  className="absolute h-full rounded-full bg-gradient-to-r"
                  style={{
                    backgroundImage: `linear-gradient(to right, ${COLORS[index % COLORS.length]}CC, ${COLORS[index % COLORS.length]})`,
                  }}
                />
                <div className="absolute inset-0 flex items-center px-3">
                  <span className="text-sm font-medium text-white/90">
                    {candidate.overallScore}%
                  </span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </motion.div>

      {/* Skills Breakdown */}
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
        className="bg-gray-800/30 rounded-xl p-6 border border-gray-700/30"
      >
        <h4 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
          <TrendingUp className="w-5 h-5 text-purple-400" />
          Skills Breakdown
        </h4>
        <div className="space-y-4">
          {candidatesData.map((candidate, index) => {
            const analysis = candidateAnalysis[candidate.key];
            return (
              <div
                key={candidate.key}
                className="p-4 bg-gray-900/50 rounded-lg border border-gray-700/50"
              >
                <div className="flex items-center justify-between mb-3">
                  <h5 className="font-medium text-white flex items-center gap-2">
                    {index === 0 && (
                      <Star className="w-4 h-4 text-purple-400" fill="currentColor" />
                    )}
                    {candidate.name}
                  </h5>
                </div>
                <div className="grid grid-cols-5 gap-2">
                  {['skills', 'experience', 'leadership', 'culturalFit', 'availability'].map(
                    metric => (
                      <div key={metric} className="text-center">
                        <div className="text-xs text-gray-400 mb-1 capitalize">
                          {metric === 'culturalFit' ? 'Culture' : metric}
                        </div>
                        <div
                          className={`text-lg font-bold ${
                            candidate[metric] >= 80
                              ? 'text-green-400'
                              : candidate[metric] >= 60
                                ? 'text-yellow-400'
                                : 'text-red-400'
                          }`}
                        >
                          {candidate[metric] || 0}%
                        </div>
                      </div>
                    )
                  )}
                </div>
                {analysis?.keyDifferentiator && (
                  <p className="text-sm text-gray-400 mt-3 pt-3 border-t border-gray-700/50">
                    {analysis.keyDifferentiator}
                  </p>
                )}
              </div>
            );
          })}
        </div>
      </motion.div>
    </div>
  );
};
