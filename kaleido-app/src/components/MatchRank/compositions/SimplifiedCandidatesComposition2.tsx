import { ICandidate } from '@/entities/interfaces';
import { useJobStore } from '@/stores/unifiedJobStore';
import { useMatchRankDetailsStore } from '@/stores/matchrankDetailsStore';
import { AnimatePresence, motion } from 'framer-motion';
import {
  ArrowLeft,
  ChevronRight,
  Medal,
  Star,
  Target,
  Trophy,
  Users,
  X,
  ChevronLeft,
  CheckSquare,
  Check,
} from 'lucide-react';
import { NavigationBurgerMenu } from '@/components/common/BurgerMenu';
import { useRouter } from 'next/navigation';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { CandidateListItem } from '../CandidateListItem';
import { UnifiedCandidateView } from '../UnifiedCandidateView';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import Image from 'next/image';
import { useCandidateSelectionStore } from '@/stores/candidateSelectionStore';
import SelectionActionsSlider from '../SelectionActionsSlider';
import ComparisonOptionsSlider from '../ComparisonOptionsSlider';
import { comparisonService } from '@/services/comparison.service';
import { ComparisonType } from '@/types/comparison.types';
import { showToast } from '@/components/Toaster';
import { GenericStatusManager } from '@/components/shared/GenericStatusManager/GenericStatusManager';
import { createComparisonConfig } from '@/components/shared/GenericStatusManager/configs/comparisonConfig';
import { useComparisonJobsStore } from '@/stores/comparisonJobsStore';
import { StatusJob } from '@/components/shared/GenericStatusManager/types';

interface GroupedCandidates {
  topTier: ICandidate[];
  secondTier: ICandidate[];
  others: ICandidate[];
  unranked: ICandidate[];
  shortlisted: ICandidate[];
}

interface SimplifiedCandidatesComposition2Props {
  candidates: ICandidate[] | GroupedCandidates;
  currentPage: number;
  onPageChange: (page: number) => void;
  isAtsJob?: boolean;
  jobId?: string;
  fetchCandidateById?: (jobId: string, candidateId: string) => Promise<ICandidate | null>;
}

// Helper to get all candidates from grouped data
const getAllCandidates = (groupedCandidates: GroupedCandidates): ICandidate[] => {
  return [
    ...groupedCandidates.shortlisted,
    ...groupedCandidates.topTier,
    ...groupedCandidates.secondTier,
    ...groupedCandidates.others,
    ...(groupedCandidates.unranked || []),
  ];
};

// Helper to get the highest ranking candidate
const getHighestRankingCandidate = (groupedCandidates: GroupedCandidates): ICandidate | null => {
  if (groupedCandidates.shortlisted?.length > 0) return groupedCandidates.shortlisted[0];
  if (groupedCandidates.topTier?.length > 0) return groupedCandidates.topTier[0];
  if (groupedCandidates.secondTier?.length > 0) return groupedCandidates.secondTier[0];
  if (groupedCandidates.others?.length > 0) return groupedCandidates.others[0];
  if (groupedCandidates.unranked?.length > 0) return groupedCandidates.unranked[0];
  return null;
};

const SimplifiedCandidatesComposition2Component: React.FC<
  SimplifiedCandidatesComposition2Props
> = ({ candidates, currentPage, onPageChange, isAtsJob = false, jobId, fetchCandidateById }) => {
  const { selectedCandidate, setSelectedCandidate } = useMatchRankDetailsStore();
  const { currentJob, fetchCandidates, stats } = useJobStore();
  const [expandedSection, setExpandedSection] = useState<string | null>('shortlisted');
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);
  const [isMobileSidebarOpen, setIsMobileSidebarOpen] = useState(false);
  const autoSelectedRef = useRef(false);
  const router = useRouter();

  // Comparison functionality states
  const [showComparisonOptions, setShowComparisonOptions] = useState(false);
  const [isComparing, setIsComparing] = useState(false);

  const { selectedCandidates, clearSelection, selectedCount, toggleCandidate, isSelected } =
    useCandidateSelectionStore();

  // Get comparison jobs from the store
  const {
    activeJobs: comparisonJobs,
    updateJob,
    removeJob,
    clearCompletedJobs,
  } = useComparisonJobsStore();

  // Convert jobs array to object format expected by GenericStatusManager
  const jobsObject = useMemo(() => {
    return comparisonJobs.reduce(
      (acc, job) => {
        acc[job.id] = job;
        return acc;
      },
      {} as Record<string, StatusJob>
    );
  }, [comparisonJobs]);

  // Get active job IDs
  const activeJobIds = useMemo(() => comparisonJobs.map(job => job.id), [comparisonJobs]);

  // Determine if candidates is already grouped
  const isGroupedData = candidates && typeof candidates === 'object' && 'topTier' in candidates;
  const groupedCandidates = isGroupedData
    ? (candidates as GroupedCandidates)
    : {
        shortlisted: [],
        topTier: [],
        secondTier: [],
        others: [],
        unranked: candidates as ICandidate[],
      };

  // Get all candidates as a flat array
  const allCandidates = useMemo(() => getAllCandidates(groupedCandidates), [groupedCandidates]);

  // Enhanced candidate selection with optimistic updates
  const handleCandidateSelect = useCallback(
    async (candidate: ICandidate) => {
      try {
        setSelectedCandidate(null);
        await new Promise(resolve => setTimeout(resolve, 50));
        setSelectedCandidate(candidate);

        if (jobId && candidate.id && fetchCandidateById) {
          const fullCandidate = await fetchCandidateById(jobId, candidate.id);
          if (fullCandidate) {
            setSelectedCandidate(fullCandidate);
          }
        }
      } catch (error) {
        console.error('Error fetching candidate details:', error);
      }
    },
    [fetchCandidateById, jobId, setSelectedCandidate]
  );

  // Handle candidate status changes
  const handleCandidateStatusChange = useCallback(async () => {
    if (!jobId) return;
    try {
      await fetchCandidates(jobId);
    } catch (error) {
      console.error('Error updating candidate status:', error);
    }
  }, [jobId, fetchCandidates]);

  // Clear selection
  const handleClearSelection = () => {
    clearSelection();
  };

  // Handle comparison
  const handleCompare = () => {
    if (selectedCount < 2) {
      showToast({
        message: 'Please select at least 2 candidates to compare',
        type: 'warning',
      });
      return;
    }
    setShowComparisonOptions(true);
  };

  // Handle comparison option selection
  const handleComparisonOptionSelect = async (type: ComparisonType, customPrompt?: string) => {
    setShowComparisonOptions(false);
    setIsComparing(true);

    try {
      const comparisonData = {
        jobId: jobId || '',
        candidateIds: selectedCandidates.map(c => c.id),
        comparisonType: type,
        userPrompt: customPrompt,
      };

      const result = await comparisonService.createComparison(comparisonData);

      // Validate the response structure
      if (!result || !result.jobId) {
        throw new Error('Invalid response from comparison service');
      }

      // Create a status job for the GenericStatusManager
      // result.jobId is the Bull queue job ID for polling
      const statusJob: StatusJob = {
        id: result.jobId, // This is the Bull queue job ID (used for polling status)
        // Don't set jobId property - let polling use the id field
        status: (result.status || 'queued') as StatusJob['status'],
        progress: 0,
        message: result.message || 'Comparison initiated',
        createdAt: new Date().toISOString(),
        metadata: {
          comparisonTitle: `Comparing ${selectedCandidates.length} candidates`,
          candidateNames: selectedCandidates.map(c => c.fullName),
          candidateCount: selectedCandidates.length,
          comparisonType: type,
          jobId: jobId, // Store job ID for navigation
          // We'll get the comparison entity ID from the completion result
        },
        result: {
          // The comparison entity ID will be returned in the completion result
        },
      };

      // Add job to the comparison jobs store
      const { addJob } = useComparisonJobsStore.getState();
      addJob(statusJob);

      showToast({
        message: 'Comparison started! You can track progress in the status manager.',
        type: 'info',
      });

      // Clear selection after starting comparison
      clearSelection();
    } catch (error) {
      console.error('Error creating comparison:', error);
      showToast({
        message: 'Failed to create comparison. Please try again.',
        isSuccess: false,
      });
    } finally {
      setIsComparing(false);
    }
  };

  // Handle other actions
  const handleSendVideoIntro = async () => {
    showToast({
      message: 'Video intro email feature coming soon!',
      type: 'info',
    });
  };

  const handleDelete = async () => {
    showToast({
      message: 'Delete feature coming soon!',
      type: 'info',
    });
  };

  const handleExport = async () => {
    showToast({
      message: 'Export feature coming soon!',
      type: 'info',
    });
  };

  const handleShare = async () => {
    showToast({
      message: 'Share feature coming soon!',
      type: 'info',
    });
  };

  // Auto-select first candidate
  useEffect(() => {
    if (allCandidates.length > 0) {
      if (!selectedCandidate && !autoSelectedRef.current) {
        const highestRankingCandidate = getHighestRankingCandidate(groupedCandidates);
        if (highestRankingCandidate) {
          autoSelectedRef.current = true;
          setSelectedCandidate(highestRankingCandidate);
        }
      } else if (selectedCandidate) {
        const candidateExists = allCandidates.some(c => c.id === selectedCandidate.id);
        if (!candidateExists) {
          const highestRankingCandidate = getHighestRankingCandidate(groupedCandidates);
          if (highestRankingCandidate) {
            setSelectedCandidate(highestRankingCandidate);
          } else {
            setSelectedCandidate(null);
          }
        }
        autoSelectedRef.current = false;
      }
    } else if (selectedCandidate) {
      setSelectedCandidate(null);
      autoSelectedRef.current = false;
    }
  }, [allCandidates, selectedCandidate?.id, groupedCandidates, setSelectedCandidate]);

  // Get threshold values - handle string values from database
  const topCandidateThreshold = currentJob?.topCandidateThreshold
    ? parseFloat(currentJob.topCandidateThreshold.toString())
    : 80;
  const secondTierCandidateThreshold = currentJob?.secondTierCandidateThreshold
    ? parseFloat(currentJob.secondTierCandidateThreshold.toString())
    : 60;

  const toggleSection = (section: string) => {
    // If candidates are selected, don't allow collapsing sections for better UX
    if (selectedCount > 0) {
      return;
    }
    setExpandedSection(prev => (prev === section ? null : section));
  };

  // Keep track of original expanded state before selection mode
  const [preSelectionExpandedSection, setPreSelectionExpandedSection] = useState<string | null>(
    null
  );

  // Expand all sections when candidates are selected for easy selection
  useEffect(() => {
    if (selectedCount > 0) {
      // Save current expanded state if we haven't already
      if (preSelectionExpandedSection === null) {
        setPreSelectionExpandedSection(expandedSection);
      }
      // We'll handle this in the rendering logic to show all sections as expanded
    } else {
      // When no candidates selected, restore previous state
      if (preSelectionExpandedSection !== null) {
        setExpandedSection(preSelectionExpandedSection);
        setPreSelectionExpandedSection(null);
      } else if (!expandedSection) {
        setExpandedSection('shortlisted');
      }
    }
  }, [selectedCount]);

  // Auto-collapse sidebar on smaller screens
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth < 1024) {
        setIsSidebarCollapsed(true);
      }
    };

    handleResize();
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  if (!selectedCandidate) {
    return (
      <div className="flex items-center justify-center h-full text-foreground">
        <p>No candidates available</p>
      </div>
    );
  }

  return (
    <TooltipProvider>
      <div className="h-screen w-screen flex flex-col">
        {/* Top Navigation Bar - Mobile Only */}
        <div className="lg:hidden flex-none bg-black/10 backdrop-blur-xl border-b border-gray-300/10 h-16 px-4 flex items-center z-50 relative">
          <NavigationBurgerMenu
            isOpen={isMobileSidebarOpen}
            onToggle={() => setIsMobileSidebarOpen(!isMobileSidebarOpen)}
            title="Candidate Rankings"
            className="absolute left-4"
          />
          <div className="flex-1 flex justify-center">
            <div className="text-sm text-gray-400">{allCandidates.length} candidates</div>
          </div>
        </div>

        {/* Main Content Area */}
        <div className="flex-1 flex relative">
          {/* Left Sidebar - Candidate List */}
          <div
            className={`
              ${isSidebarCollapsed ? 'w-20' : 'w-80'} 
              ${isMobileSidebarOpen ? 'w-80 translate-x-0' : '-translate-x-full lg:translate-x-0'}
              fixed lg:relative inset-y-0 left-0 z-40 lg:z-auto
              ${isMobileSidebarOpen ? 'top-16 lg:top-0' : 'top-0'}
              flex-shrink-0 border-r border-gray-300/10 bg-black/10 backdrop-blur-xl
              flex flex-col transition-all duration-300
              lg:h-full h-[calc(100vh-4rem)]
            `}
          >
            <div>
              {/* Back to Jobs Button with collapse toggle */}
              <div
                className={`${isSidebarCollapsed ? 'px-3' : 'px-6'} py-6 border-b border-gray-300/10 flex items-center justify-between h-[80px]`}
              >
                {!isSidebarCollapsed ? (
                  <>
                    <button
                      onClick={() => router.push('/jobs')}
                      className="flex items-center gap-2 text-muted-foreground hover:text-foreground transition-colors"
                    >
                      <ArrowLeft className="w-5 h-5" />
                      <span>Back to Jobs</span>
                    </button>
                    <button
                      onClick={() => setIsSidebarCollapsed(true)}
                      className="hidden lg:block p-1.5 rounded hover:bg-gray-800/20 transition-colors"
                    >
                      <ChevronLeft className="w-4 h-4 text-gray-400" />
                    </button>
                  </>
                ) : (
                  <div className="w-full flex flex-col items-center gap-2">
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <button
                          onClick={() => router.push('/jobs')}
                          className="p-2 rounded hover:bg-gray-800/20 transition-colors"
                        >
                          <ArrowLeft className="w-4 h-4 text-gray-400" />
                        </button>
                      </TooltipTrigger>
                      <TooltipContent side="right">Back to Jobs</TooltipContent>
                    </Tooltip>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <button
                          onClick={() => setIsSidebarCollapsed(false)}
                          className="hidden lg:block p-1.5 rounded hover:bg-gray-800/20 transition-colors"
                        >
                          <ChevronRight className="w-4 h-4 text-gray-400" />
                        </button>
                      </TooltipTrigger>
                      <TooltipContent side="right">Expand Sidebar</TooltipContent>
                    </Tooltip>
                  </div>
                )}

                {/* Mobile close button */}
                <button
                  onClick={() => setIsMobileSidebarOpen(false)}
                  className="lg:hidden p-1.5 rounded hover:bg-gray-800/20 transition-colors"
                >
                  <X className="w-4 h-4 text-gray-400" />
                </button>
              </div>
            </div>

            {/* Candidate Rankings section - scrollable */}
            <div className="flex-1 overflow-y-auto">
              <div
                className={`${isSidebarCollapsed && !isMobileSidebarOpen ? 'px-2 py-4' : 'p-4'}`}
              >
                {(!isSidebarCollapsed || isMobileSidebarOpen) && (
                  <div className="mb-3 space-y-2">
                    <div className="flex items-center justify-between">
                      <h2 className="text-base font-semibold text-gray-300">Candidate Rankings</h2>
                      {selectedCount > 0 && (
                        <motion.button
                          initial={{ opacity: 0, scale: 0.9 }}
                          animate={{ opacity: 1, scale: 1 }}
                          whileTap={{ scale: 0.95 }}
                          onClick={handleClearSelection}
                          className="text-xs text-purple-400 hover:text-purple-300 transition-colors"
                        >
                          Clear ({selectedCount})
                        </motion.button>
                      )}
                    </div>
                  </div>
                )}

                {/* Shortlisted - Always show even if empty */}
                <CandidateSection
                  title="Shortlisted"
                  candidates={groupedCandidates.shortlisted}
                  icon={<Star className="w-3.5 h-3.5 text-yellow-500/80 fill-yellow-500/80" />}
                  isExpanded={selectedCount > 0 || expandedSection === 'shortlisted'}
                  onToggle={() => toggleSection('shortlisted')}
                  selectedCandidateId={selectedCandidate?.id}
                  onSelectCandidate={handleCandidateSelect}
                  isCollapsed={isSidebarCollapsed && !isMobileSidebarOpen}
                  onMobileCandidateSelect={() => setIsMobileSidebarOpen(false)}
                  isSelectionMode={true}
                  selectedCandidates={selectedCandidates}
                  onToggleSelection={toggleCandidate}
                  isSelected={isSelected}
                  isDisabled={selectedCount > 0}
                />

                {/* Top Candidates - Filter out shortlisted */}
                <CandidateSection
                  title="Top Candidates"
                  candidates={groupedCandidates.topTier.filter(c => c.status !== 'SHORTLISTED')}
                  icon={<Trophy className="w-3.5 h-3.5 text-purple-400/80" />}
                  isExpanded={selectedCount > 0 || expandedSection === 'top-tier'}
                  onToggle={() => toggleSection('top-tier')}
                  selectedCandidateId={selectedCandidate?.id}
                  onSelectCandidate={handleCandidateSelect}
                  isCollapsed={isSidebarCollapsed && !isMobileSidebarOpen}
                  onMobileCandidateSelect={() => setIsMobileSidebarOpen(false)}
                  isSelectionMode={true}
                  selectedCandidates={selectedCandidates}
                  onToggleSelection={toggleCandidate}
                  isSelected={isSelected}
                  isDisabled={selectedCount > 0}
                />

                {/* Second Tier - Filter out shortlisted */}
                <CandidateSection
                  title="Second Tier"
                  candidates={groupedCandidates.secondTier.filter(c => c.status !== 'SHORTLISTED')}
                  icon={<Medal className="w-3.5 h-3.5 text-gray-400/80" />}
                  isExpanded={selectedCount > 0 || expandedSection === 'second-tier'}
                  onToggle={() => toggleSection('second-tier')}
                  selectedCandidateId={selectedCandidate?.id}
                  onSelectCandidate={handleCandidateSelect}
                  isCollapsed={isSidebarCollapsed && !isMobileSidebarOpen}
                  onMobileCandidateSelect={() => setIsMobileSidebarOpen(false)}
                  isSelectionMode={true}
                  selectedCandidates={selectedCandidates}
                  onToggleSelection={toggleCandidate}
                  isSelected={isSelected}
                  isDisabled={selectedCount > 0}
                />

                {/* Other Candidates - Filter out shortlisted */}
                {groupedCandidates.others.filter(c => c.status !== 'SHORTLISTED').length > 0 && (
                  <CandidateSection
                    title="Other Candidates"
                    candidates={groupedCandidates.others.filter(c => c.status !== 'SHORTLISTED')}
                    icon={<Target className="w-3.5 h-3.5 text-gray-400/60" />}
                    isExpanded={selectedCount > 0 || expandedSection === 'others'}
                    onToggle={() => toggleSection('others')}
                    selectedCandidateId={selectedCandidate?.id}
                    onSelectCandidate={handleCandidateSelect}
                    isCollapsed={isSidebarCollapsed}
                    onMobileCandidateSelect={() => setIsMobileSidebarOpen(false)}
                    isSelectionMode={true}
                    selectedCandidates={selectedCandidates}
                    onToggleSelection={toggleCandidate}
                    isSelected={isSelected}
                    isDisabled={selectedCount > 0}
                  />
                )}

                {/* Unranked - Filter out shortlisted */}
                {groupedCandidates.unranked.filter(c => c.status !== 'SHORTLISTED').length > 0 && (
                  <CandidateSection
                    title="Unranked"
                    candidates={groupedCandidates.unranked.filter(c => c.status !== 'SHORTLISTED')}
                    icon={<Users className="w-3.5 h-3.5 text-gray-400/60" />}
                    isExpanded={selectedCount > 0 || expandedSection === 'unranked'}
                    onToggle={() => toggleSection('unranked')}
                    selectedCandidateId={selectedCandidate?.id}
                    onSelectCandidate={handleCandidateSelect}
                    isCollapsed={isSidebarCollapsed}
                    onMobileCandidateSelect={() => setIsMobileSidebarOpen(false)}
                    isSelectionMode={true}
                    selectedCandidates={selectedCandidates}
                    onToggleSelection={toggleCandidate}
                    isSelected={isSelected}
                    isDisabled={selectedCount > 0}
                  />
                )}
              </div>
            </div>

            {/* Kaleido Logo at bottom */}
            <div className="p-4">
              <Image
                src="/images/logos/kaleido-logo-only.webp"
                alt="Kaleido"
                width={isSidebarCollapsed && !isMobileSidebarOpen ? 40 : 56}
                height={isSidebarCollapsed && !isMobileSidebarOpen ? 40 : 56}
                className="transition-all"
              />
            </div>
          </div>

          {/* Mobile Overlay */}
          {isMobileSidebarOpen && (
            <div
              className="fixed inset-0 bg-black/50 z-30 lg:hidden top-16"
              onClick={() => setIsMobileSidebarOpen(false)}
            />
          )}

          {/* Right Content - Candidate Details */}
          <div className="flex-1 overflow-hidden">
            <UnifiedCandidateView
              candidate={selectedCandidate}
              jobId={jobId || ''}
              jobTitle={currentJob?.jobTitle || currentJob?.jobType}
              onCandidateSelect={handleCandidateSelect}
              onStatusUpdate={handleCandidateStatusChange}
              topCandidateThreshold={topCandidateThreshold}
              secondTierCandidateThreshold={secondTierCandidateThreshold}
              stats={stats}
            />
          </div>
        </div>

        {/* Selection Actions Slider */}
        <SelectionActionsSlider
          isVisible={selectedCount > 0}
          selectedCandidates={selectedCandidates}
          onClose={handleClearSelection}
          onCompare={handleCompare}
          onSendVideoIntro={handleSendVideoIntro}
          onDelete={handleDelete}
          onExport={handleExport}
          onShare={handleShare}
        />

        {/* Comparison Options Slider */}
        <ComparisonOptionsSlider
          isVisible={showComparisonOptions}
          selectedCandidates={selectedCandidates}
          onClose={() => setShowComparisonOptions(false)}
          onSelectOption={handleComparisonOptionSelect}
        />

        {/* Loading overlay */}
        {isComparing && (
          <div className="fixed inset-0 z-[70] bg-black/50 backdrop-blur-sm flex items-center justify-center">
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              className="bg-card border border-border rounded-lg p-6 flex flex-col items-center gap-4"
            >
              <div className="w-12 h-12 border-4 border-purple-500 border-t-transparent rounded-full animate-spin" />
              <p className="text-lg font-medium">Creating comparison...</p>
              <p className="text-sm text-muted-foreground">This may take a moment</p>
            </motion.div>
          </div>
        )}
        {/* GenericStatusManager for comparison jobs */}
        <GenericStatusManager
          jobs={jobsObject}
          activeJobs={activeJobIds}
          config={createComparisonConfig()}
          onUpdateJob={(jobId: string, updates: Partial<StatusJob>) => {
            updateJob(jobId, updates);
          }}
          onRemoveJob={(jobId: string) => {
            removeJob(jobId);
          }}
          onClearCompleted={() => {
            clearCompletedJobs();
          }}
        />
      </div>
    </TooltipProvider>
  );
};

// Candidate Section Component
interface CandidateSectionProps {
  title: string;
  candidates: ICandidate[];
  icon: React.ReactNode;
  isExpanded: boolean;
  onToggle: () => void;
  selectedCandidateId?: string;
  onSelectCandidate: (candidate: ICandidate) => void;
  isCollapsed?: boolean;
  onMobileCandidateSelect?: () => void;
  isSelectionMode?: boolean;
  selectedCandidates?: ICandidate[];
  onToggleSelection?: (candidate: ICandidate) => void;
  isSelected?: (candidateId: string) => boolean;
  isDisabled?: boolean;
}

const CandidateSection: React.FC<CandidateSectionProps> = ({
  title,
  candidates,
  icon,
  isExpanded,
  onToggle,
  selectedCandidateId,
  onSelectCandidate,
  isCollapsed = false,
  onMobileCandidateSelect,
  isSelectionMode = false,
  selectedCandidates = [],
  onToggleSelection,
  isSelected,
  isDisabled = false,
}) => {
  const handleCandidateSelect = (candidate: ICandidate) => {
    onSelectCandidate(candidate);
    // Close mobile menu on candidate selection for small screens
    if (typeof window !== 'undefined' && window.innerWidth < 1024) {
      onMobileCandidateSelect?.();
    }
  };

  if (isCollapsed) {
    // Collapsed view - show only icon with candidate count
    return (
      <div className="mb-2 flex justify-center">
        <Tooltip>
          <TooltipTrigger asChild>
            <button
              onClick={onToggle}
              className="p-2 hover:bg-white/[0.02] rounded-lg transition-all duration-200 group relative"
            >
              <div className="opacity-60 group-hover:opacity-80 transition-opacity">{icon}</div>
              {candidates.length > 0 && (
                <span className="absolute -top-1 -right-1 text-[9px] font-bold text-white bg-purple-500 rounded-full w-4 h-4 flex items-center justify-center">
                  {candidates.length}
                </span>
              )}
            </button>
          </TooltipTrigger>
          <TooltipContent side="right">
            <div>
              <p className="font-medium">{title}</p>
              <p className="text-xs text-gray-400 mt-1">{candidates.length} candidates</p>
            </div>
          </TooltipContent>
        </Tooltip>
      </div>
    );
  }

  return (
    <div className="mb-2">
      <button
        onClick={onToggle}
        disabled={isDisabled}
        className={`w-full flex items-center justify-between p-2 rounded-lg transition-all duration-200 group ${
          isDisabled ? 'opacity-50 cursor-not-allowed' : 'hover:bg-white/[0.02] cursor-pointer'
        }`}
      >
        <div className="flex items-center gap-2.5">
          <div className="opacity-60 group-hover:opacity-80 transition-opacity">{icon}</div>
          <span className="font-medium text-sm text-gray-300 group-hover:text-gray-100 transition-colors">
            {title}
          </span>
        </div>
        <div className="flex items-center gap-2">
          <span className="text-[11px] font-medium text-gray-500 bg-gray-800/50 px-2 py-0.5 rounded-md">
            {candidates.length}
          </span>
          <ChevronRight
            className={`w-3.5 h-3.5 text-gray-500 transition-transform ${isExpanded ? 'rotate-90' : ''}`}
          />
        </div>
      </button>

      <AnimatePresence>
        {isExpanded && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.2 }}
            className="overflow-hidden"
          >
            <div className="pl-1 pr-1 py-1 space-y-0.5">
              {candidates.length > 0 ? (
                candidates.map(candidate => (
                  <CandidateItem
                    key={candidate.id}
                    candidate={candidate}
                    isSelected={selectedCandidateId === candidate.id}
                    onSelect={handleCandidateSelect}
                    isSelectionMode={true}
                    isChecked={isSelected ? isSelected(candidate.id) : false}
                    onToggleSelection={onToggleSelection}
                  />
                ))
              ) : (
                <p className="text-sm text-gray-500 italic py-2">No candidates</p>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

// Enhanced Candidate Item Component with selection checkbox
const CandidateItem: React.FC<{
  candidate: ICandidate;
  isSelected: boolean;
  onSelect: (candidate: ICandidate) => void;
  isSelectionMode?: boolean;
  isChecked?: boolean;
  onToggleSelection?: (candidate: ICandidate) => void;
}> = ({
  candidate,
  isSelected,
  onSelect,
  isSelectionMode = false,
  isChecked = false,
  onToggleSelection,
}) => {
  const handleCheckboxClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onToggleSelection) {
      onToggleSelection(candidate);
    }
  };

  const { selectedCandidates, canAddMore } = useCandidateSelectionStore();

  return (
    <div className="relative flex items-center group">
      {/* Checkbox - always visible */}
      <motion.div
        initial={{ opacity: 0, x: -10 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.2 }}
        className="absolute left-0 z-10"
      >
        <button
          onClick={handleCheckboxClick}
          disabled={!canAddMore && !isChecked}
          className={`
            w-5 h-5 rounded border-2 transition-all duration-200 flex items-center justify-center
            ${
              isChecked
                ? 'bg-purple-500 border-purple-500'
                : canAddMore
                  ? 'bg-white/10 border-gray-400 hover:border-purple-400'
                  : 'bg-white/5 border-gray-600 cursor-not-allowed opacity-50'
            }
          `}
          title={!canAddMore && !isChecked ? 'Maximum 3 candidates can be selected' : ''}
        >
          {isChecked && <Check className="w-3 h-3 text-white" strokeWidth={3} />}
        </button>
      </motion.div>

      {/* Candidate list item */}
      <div className="flex-1 ml-8 transition-all duration-200">
        <CandidateListItem
          candidate={candidate}
          isSelected={isSelected}
          onSelect={onSelect}
          isShortlisted={candidate.status === 'SHORTLISTED'}
        />
      </div>
    </div>
  );
};

// Memoized component to prevent unnecessary re-renders
export const SimplifiedCandidatesComposition2 = React.memo(
  SimplifiedCandidatesComposition2Component,
  (prevProps, nextProps) => {
    return (
      prevProps.currentPage === nextProps.currentPage &&
      prevProps.isAtsJob === nextProps.isAtsJob &&
      prevProps.jobId === nextProps.jobId &&
      JSON.stringify(prevProps.candidates) === JSON.stringify(nextProps.candidates)
    );
  }
);
