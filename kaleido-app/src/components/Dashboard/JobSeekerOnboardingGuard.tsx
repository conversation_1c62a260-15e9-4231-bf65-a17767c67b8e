import React, { useEffect } from 'react';
import { useRouter } from 'next/router';
import { UserRole } from '@/shared/types';

interface JobSeekerProfile {
  id?: string;
  hasCompletedOnboarding?: boolean;
  onboardingRequired?: boolean;
}

interface JobSeekerOnboardingGuardProps {
  userData: {
    userRole: UserRole;
    dashboardStats?: {
      onboardingStatus?: {
        hasCompletedOnboarding: boolean;
        canApplyForJobs: boolean;
      };
    };
    profile?: JobSeekerProfile | null;
  };
  children: React.ReactNode;
  onboardingData: {
    shouldShowSetupSlider: boolean;
    hasCheckedOnboarding: boolean;
  };
}

/**
 * Guard component that checks if a job seeker needs onboarding
 * and prevents rendering dashboard content until onboarding check is complete
 */
export const JobSeekerOnboardingGuard: React.FC<JobSeekerOnboardingGuardProps> = ({
  userData,
  children,
  onboardingData,
}) => {
  const router = useRouter();

  useEffect(() => {
    // Only check for job seekers
    if (userData.userRole !== UserRole.JOB_SEEKER) {
      return;
    }

    // Check if we've determined onboarding is needed
    if (onboardingData.hasCheckedOnboarding && onboardingData.shouldShowSetupSlider) {
      // For job seekers, we don't redirect to a separate page
      // The dashboard page handles showing the setup slider
      return;
    }

    // Check dashboard stats for onboarding status
    const hasCompletedOnboarding = userData.dashboardStats?.onboardingStatus?.hasCompletedOnboarding;
    
    // If explicitly needs onboarding based on backend data
    if (hasCompletedOnboarding === false) {
      // This will be handled by the dashboard page showing the setup slider
      return;
    }
  }, [userData, onboardingData, router]);

  // Don't render children if job seeker needs onboarding and we're still checking
  if (
    userData.userRole === UserRole.JOB_SEEKER &&
    !onboardingData.hasCheckedOnboarding
  ) {
    // Return null to prevent flash of dashboard before onboarding check
    return null;
  }

  // Don't render dashboard if setup slider should be shown
  if (
    userData.userRole === UserRole.JOB_SEEKER &&
    onboardingData.shouldShowSetupSlider
  ) {
    // The parent component will show the setup slider instead
    return null;
  }

  return <>{children}</>;
};

export default JobSeekerOnboardingGuard;