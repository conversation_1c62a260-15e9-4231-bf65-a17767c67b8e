import React from 'react';
import { render, screen, waitFor, act } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { useRouter } from 'next/navigation';
import Dashboard from '../Dashboard';
import * as useEnhancedUserDataModule from '@/hooks/useEnhancedUserData';
import * as useAuthSyncModule from '@/hooks/useAuthSync';
import * as useAuthStoreModule from '@/stores/authStore';
import * as useOnboardingStoreModule from '@/stores/onboardingStore';
import { UserRole } from '@/shared/types';

// Mock dependencies
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
  useSearchParams: jest.fn(() => new URLSearchParams()),
}));
jest.mock('next/router', () => ({
  useRouter: jest.fn(() => ({
    push: jest.fn(),
    replace: jest.fn(),
    query: {},
    pathname: '/dashboard',
  })),
}));
jest.mock('@/hooks/useAuthSync');
jest.mock('@/stores/authStore');
jest.mock('@/hooks/useEnhancedUserData');
jest.mock('@/stores/onboardingStore', () => ({
  useOnboardingStore: jest.fn(() => ({
    profile: null,
    company: null,
    isLoading: false,
    error: null,
    shouldShowSetupSlider: false,
    hasCheckedOnboarding: false,
    userRole: null,
    fetchProfile: jest.fn(),
    fetchCompany: jest.fn(),
    setProfile: jest.fn(),
    setCompany: jest.fn(),
    setShouldShowSetupSlider: jest.fn(),
    updateOnboardingProgress: jest.fn(),
    markOnboardingComplete: jest.fn(),
    markCompanyOnboardingComplete: jest.fn(),
    setUserRole: jest.fn(),
    reset: jest.fn(),
    getOverallProgress: jest.fn(() => 0),
    getNeedsOnboarding: jest.fn(() => false),
    getCompanyNeedsOnboarding: jest.fn(() => false),
    getMissingRequiredFields: jest.fn(() => []),
    shouldRedirectToOnboarding: jest.fn(() => null),
  })),
}));

// Mock Auth0 UserProvider
jest.mock('@auth0/nextjs-auth0/client', () => ({
  UserProvider: ({ children }: { children: React.ReactNode }) => children,
  useUser: jest.fn(() => ({
    user: { sub: 'test-user-id' },
    isLoading: false,
  })),
}));
jest.mock('@/hooks/useProfileCompletion', () => ({
  useProfileCompletion: () => ({
    isModalOpen: false,
    missingFields: [],
    closeModal: jest.fn(),
    handleComplete: jest.fn(),
  }),
}));
jest.mock('@/hooks/useProactiveProfileValidation', () => ({
  __esModule: true,
  default: () => ({
    shouldShowAlert: false,
    validation: null,
    dismissAlert: jest.fn(),
  }),
}));

// Mock components
jest.mock('../JobSeeker/JobSeekerDashboard', () => ({
  __esModule: true,
  default: ({ stats, loading }: any) => (
    <div data-testid="jobseeker-dashboard">
      Job Seeker Dashboard - Loading: {loading ? 'true' : 'false'}
    </div>
  ),
}));

jest.mock('../../JobSeeker/JobSeekerSetupSlider', () => ({
  JobSeekerSetupSlider: ({ onComplete, onClose }: any) => (
    <div data-testid="jobseeker-setup-slider">
      <button onClick={() => onComplete({})}>Complete Setup</button>
      <button onClick={onClose}>Close</button>
    </div>
  ),
}));

jest.mock('../../Layouts/ColourfulLoader', () => ({
  __esModule: true,
  default: () => <div data-testid="loader">Loading...</div>,
}));

jest.mock('../../steps/layout/AppLayout', () => ({
  __esModule: true,
  default: ({ children, isLoading }: any) => (
    isLoading ? <div className="min-h-screen flex items-center justify-center"><div data-testid="loader">Loading...</div></div> : children
  ),
}));

jest.mock('../../Auth/RoleBasedRedirectHandler', () => ({
  __esModule: true,
  default: () => null,
}));

jest.mock('../EmployerOnboardingGuard', () => ({
  EmployerOnboardingGuard: ({ children }: any) => children,
}));

jest.mock('../JobSeekerOnboardingGuard', () => ({
  JobSeekerOnboardingGuard: ({ children }: any) => children,
}));

jest.mock('../../JobSeeker/components/IncompleteProfileAlert', () => ({
  IncompleteProfileAlert: () => null,
}));

jest.mock('../../JobSeeker/components/ProfileDataCollectionModal', () => ({
  ProfileDataCollectionModal: () => null,
}));

jest.mock('@/contexts/jobSearch/JobSearchContext', () => ({
  JobSearchProvider: ({ children }: any) => children,
}));

describe('Job Seeker Onboarding Flow', () => {
  let queryClient: QueryClient;
  const mockPush = jest.fn();
  const mockReplace = jest.fn();

  beforeEach(() => {
    queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false },
      },
    });

    (useRouter as jest.Mock).mockReturnValue({
      push: mockPush,
      replace: mockReplace,
      query: {},
      pathname: '/dashboard',
    });

    // Default auth state
    (useAuthSyncModule.useAuthSync as jest.Mock).mockReturnValue(undefined);
    (useAuthStoreModule.useAuthStore as any).mockReturnValue({
      isAuthenticated: true,
      isLoading: false,
      isInitialized: true,
      session: { user: { sub: 'test-user-id' } },
    });

    // Default onboarding store state
    (useOnboardingStoreModule.useOnboardingStore as any).mockReturnValue({
      profile: null,
      isLoading: false,
      shouldShowSetupSlider: false,
      hasCheckedOnboarding: true,
      fetchProfile: jest.fn(),
      setShouldShowSetupSlider: jest.fn(),
      markOnboardingComplete: jest.fn(),
      setUserRole: jest.fn(),
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  const renderWithProviders = (component: React.ReactElement) => {
    return render(
      <QueryClientProvider client={queryClient}>
        {component}
      </QueryClientProvider>
    );
  };

  describe('Race Condition Tests', () => {
    it('should show loading state while checking job seeker onboarding status', async () => {
      // Mock enhanced user data as loading initially
      (useEnhancedUserDataModule.default as jest.Mock).mockReturnValue({
        userData: null,
        isLoading: true,
        error: null,
      });

      renderWithProviders(<Dashboard />);

      // Should show loader while data is loading
      expect(screen.getByTestId('loader')).toBeInTheDocument();
      expect(screen.queryByTestId('jobseeker-dashboard')).not.toBeInTheDocument();
      expect(screen.queryByTestId('jobseeker-setup-slider')).not.toBeInTheDocument();
    });

    it('should not render job seeker dashboard until onboarding check is complete', async () => {
      // Start with loading state
      const mockUseEnhancedUserData = useEnhancedUserDataModule.default as jest.Mock;
      const mockOnboardingStore = useOnboardingStoreModule.useOnboardingStore as any;

      mockUseEnhancedUserData.mockReturnValue({
        userData: null,
        isLoading: true,
        error: null,
      });

      // Onboarding store hasn't checked yet
      mockOnboardingStore.mockReturnValue({
        profile: null,
        isLoading: false,
        shouldShowSetupSlider: false,
        hasCheckedOnboarding: false,
        fetchProfile: jest.fn(),
        setShouldShowSetupSlider: jest.fn(),
        markOnboardingComplete: jest.fn(),
        setUserRole: jest.fn(),
      });

      const { rerender } = renderWithProviders(<Dashboard />);

      // Verify loading state
      expect(screen.getByTestId('loader')).toBeInTheDocument();

      // Simulate data loaded but onboarding not checked yet
      act(() => {
        mockUseEnhancedUserData.mockReturnValue({
          userData: {
            userRole: UserRole.JOB_SEEKER,
            dashboardStats: {},
            profile: {
              hasCompletedOnboarding: false,
            },
          },
          isLoading: false,
          error: null,
        });
      });

      rerender(
        <QueryClientProvider client={queryClient}>
          <Dashboard />
        </QueryClientProvider>
      );

      // Should not show job seeker dashboard while checking onboarding
      await waitFor(() => {
        expect(screen.queryByTestId('jobseeker-dashboard')).not.toBeInTheDocument();
      });
    });

    it('should redirect to onboarding when job seeker needs onboarding', async () => {
      // Mock job seeker needing onboarding
      (useEnhancedUserDataModule.default as jest.Mock).mockReturnValue({
        userData: {
          userRole: UserRole.JOB_SEEKER,
          dashboardStats: {},
          profile: {
            hasCompletedOnboarding: false,
          },
        },
        isLoading: false,
        error: null,
      });

      // Mock onboarding store indicating setup slider should show
      (useOnboardingStoreModule.useOnboardingStore as any).mockReturnValue({
        profile: { hasCompletedOnboarding: false },
        isLoading: false,
        shouldShowSetupSlider: true,
        hasCheckedOnboarding: true,
        fetchProfile: jest.fn(),
        setShouldShowSetupSlider: jest.fn(),
        markOnboardingComplete: jest.fn(),
        setUserRole: jest.fn(),
      });

      renderWithProviders(<Dashboard />);

      // Should redirect to jobseeker onboarding
      await waitFor(() => {
        expect(mockReplace).toHaveBeenCalledWith('/jobseeker-onboarding');
      });
      
      // Should not render dashboard
      expect(screen.queryByTestId('jobseeker-dashboard')).not.toBeInTheDocument();
    });

    it('should show job seeker dashboard when onboarding is complete', async () => {
      // Mock job seeker with onboarding complete
      (useEnhancedUserDataModule.default as jest.Mock).mockReturnValue({
        userData: {
          userRole: UserRole.JOB_SEEKER,
          dashboardStats: {
            applications: { total: 5 },
            matchedJobs: { total: 10 },
            onboardingStatus: {
              hasCompletedOnboarding: true,
              canApplyForJobs: true,
            },
          },
        },
        isLoading: false,
        error: null,
      });

      // Mock onboarding store indicating no setup needed
      (useOnboardingStoreModule.useOnboardingStore as any).mockReturnValue({
        profile: { hasCompletedOnboarding: true },
        isLoading: false,
        shouldShowSetupSlider: false,
        hasCheckedOnboarding: true,
        fetchProfile: jest.fn(),
        setShouldShowSetupSlider: jest.fn(),
        markOnboardingComplete: jest.fn(),
        setUserRole: jest.fn(),
      });

      renderWithProviders(<Dashboard />);

      // Should show job seeker dashboard
      await waitFor(() => {
        expect(screen.getByTestId('jobseeker-dashboard')).toBeInTheDocument();
        expect(screen.getByText(/Loading: false/)).toBeInTheDocument();
        expect(screen.queryByTestId('jobseeker-setup-slider')).not.toBeInTheDocument();
      });
    });

    it('should handle job seeker without profile data gracefully', async () => {
      // Mock job seeker without profile data
      (useEnhancedUserDataModule.default as jest.Mock).mockReturnValue({
        userData: {
          userRole: UserRole.JOB_SEEKER,
          dashboardStats: {},
        },
        isLoading: false,
        error: null,
      });

      renderWithProviders(<Dashboard />);

      // Should eventually show dashboard (depends on onboarding store check)
      await waitFor(() => {
        expect(
          screen.getByTestId('jobseeker-dashboard') || 
          screen.getByTestId('jobseeker-setup-slider')
        ).toBeInTheDocument();
      });
    });

    it('should handle transition from loading to setup slider smoothly', async () => {
      const mockUseEnhancedUserData = useEnhancedUserDataModule.default as jest.Mock;
      const mockOnboardingStore = useOnboardingStoreModule.useOnboardingStore as any;
      
      // Track render states
      const renderStates: string[] = [];

      // Initial loading state
      mockUseEnhancedUserData.mockReturnValue({
        userData: null,
        isLoading: true,
        error: null,
      });

      mockOnboardingStore.mockReturnValue({
        profile: null,
        isLoading: false,
        shouldShowSetupSlider: false,
        hasCheckedOnboarding: false,
        fetchProfile: jest.fn(),
        setShouldShowSetupSlider: jest.fn(),
        markOnboardingComplete: jest.fn(),
        setUserRole: jest.fn(),
      });

      const TestWrapper = () => {
        const { userData, isLoading } = useEnhancedUserDataModule.default();
        const { shouldShowSetupSlider, hasCheckedOnboarding } = useOnboardingStoreModule.useOnboardingStore();
        
        if (isLoading || !userData) {
          renderStates.push('loading');
          return <div data-testid="loader">Loading...</div>;
        }
        
        if (userData.userRole === UserRole.JOB_SEEKER) {
          if (!hasCheckedOnboarding) {
            renderStates.push('checking');
            return null;
          }
          
          if (shouldShowSetupSlider) {
            renderStates.push('setup-slider');
            return <div data-testid="jobseeker-setup-slider">Setup Slider</div>;
          }
          
          renderStates.push('dashboard');
          return <div data-testid="jobseeker-dashboard">Dashboard</div>;
        }
        
        return null;
      };

      const { rerender } = renderWithProviders(<TestWrapper />);

      // Update to loaded state with onboarding needed
      act(() => {
        mockUseEnhancedUserData.mockReturnValue({
          userData: {
            userRole: UserRole.JOB_SEEKER,
            dashboardStats: {
              onboardingStatus: {
                hasCompletedOnboarding: false,
              },
            },
          },
          isLoading: false,
          error: null,
        });

        mockOnboardingStore.mockReturnValue({
          profile: null,
          isLoading: false,
          shouldShowSetupSlider: true,
          hasCheckedOnboarding: true,
          fetchProfile: jest.fn(),
          setShouldShowSetupSlider: jest.fn(),
          markOnboardingComplete: jest.fn(),
          setUserRole: jest.fn(),
        });
      });

      rerender(
        <QueryClientProvider client={queryClient}>
          <TestWrapper />
        </QueryClientProvider>
      );

      // Verify smooth transition without flickering
      expect(renderStates).toEqual(['loading', 'setup-slider']);
    });
  });

  describe('Dashboard Page Integration', () => {
    it('should properly integrate with dashboard page for job seekers', async () => {
      // Import the actual dashboard page component
      const DashboardPage = require('@/pages/dashboard').default;
      
      // Mock initial state
      (useEnhancedUserDataModule.default as jest.Mock).mockReturnValue({
        userData: {
          userRole: UserRole.JOB_SEEKER,
          dashboardStats: {
            onboardingStatus: {
              hasCompletedOnboarding: false,
            },
          },
        },
        isLoading: false,
        error: null,
      });

      (useOnboardingStoreModule.useOnboardingStore as any).mockReturnValue({
        profile: null,
        isLoading: false,
        shouldShowSetupSlider: true,
        hasCheckedOnboarding: true,
        fetchProfile: jest.fn(),
        setShouldShowSetupSlider: jest.fn(),
        markOnboardingComplete: jest.fn(),
        setUserRole: jest.fn(),
      });

      renderWithProviders(<DashboardPage />);

      // Should show setup slider for job seeker needing onboarding
      await waitFor(() => {
        expect(screen.getByTestId('jobseeker-setup-slider')).toBeInTheDocument();
        expect(screen.queryByTestId('jobseeker-dashboard')).not.toBeInTheDocument();
      });
    });
  });
});