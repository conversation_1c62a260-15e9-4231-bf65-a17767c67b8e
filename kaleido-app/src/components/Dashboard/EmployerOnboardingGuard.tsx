import React, { useEffect } from 'react';
import { useRouter } from 'next/router';
import { UserRole } from '@/shared/types';

interface EmployerOnboardingGuardProps {
  userData: {
    userRole: UserRole;
    company?: {
      onboardingRequired?: boolean;
      id?: string;
    } | null;
  };
  children: React.ReactNode;
}

/**
 * Guard component that checks if an employer needs onboarding
 * and redirects them if necessary before rendering children
 */
export const EmployerOnboardingGuard: React.FC<EmployerOnboardingGuardProps> = ({
  userData,
  children,
}) => {
  const router = useRouter();

  useEffect(() => {
    // Only check for employers
    if (userData.userRole !== UserRole.EMPLOYER) {
      return;
    }

    // Check if company exists and needs onboarding
    if (userData.company && userData.company.onboardingRequired === true) {
      router.replace('/company-onboarding');
    }
  }, [userData, router]);

  // Don't render children if employer needs onboarding
  if (
    userData.userRole === UserRole.EMPLOYER &&
    userData.company &&
    userData.company.onboardingRequired === true
  ) {
    // Return null to prevent flash of dashboard before redirect
    return null;
  }

  return <>{children}</>;
};

export default EmployerOnboardingGuard;