'use client';

import React, { useEffect } from 'react';
import { useRouter } from 'next/navigation';

import useEnhancedUserData from '@/hooks/useEnhancedUserData';
import { UserRole } from '@/shared/types';

import ColorfulSmokeyOrbLoader from '../Layouts/ColourfulLoader';
import EmployerDashboard from './Employer/EmployerDashboard';
import GraduateDashboard from './Graduate/GraduateDashboard';
import JobSeekerDashboard from './JobSeeker/JobSeekerDashboard';

export const Dashboard = () => {
  // Use the new enhanced user data hook that provides authoritative role from backend
  const { userData, isLoading, error } = useEnhancedUserData();
  const router = useRouter();

  // Check if user needs onboarding
  useEffect(() => {
    if (!userData || isLoading) return;

    // Check for employers
    if (userData.userRole === UserRole.EMPLOYER) {
      // Check if company exists and needs onboarding
      if (userData.company && userData.company.onboardingRequired === true) {
        router.replace('/company-onboarding');
      }
    }
    
    // Check for job seekers and graduates
    if (userData.userRole === UserRole.JOB_SEEKER || userData.userRole === UserRole.GRADUATE) {
      // Check if profile exists and needs onboarding
      if (userData.profile && !userData.profile.hasCompletedOnboarding) {
        router.replace('/jobseeker-onboarding');
      }
    }
  }, [userData, isLoading, router]);

  // Show loading state while data is being fetched
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[80vh]">
        <ColorfulSmokeyOrbLoader />
      </div>
    );
  }

  if (error) {
    // Check if this is an authentication error
    const authError = error as any;
    if (authError?.status === 403 || authError?.status === 401 || authError?.requiresAuth) {
      // Don't show error UI for auth issues - let the error boundary or auth system handle it
      return null;
    }

    return (
      <div className="flex items-center justify-center min-h-[80vh]">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-red-600 mb-2">Error Loading Dashboard</h2>
          <p className="text-gray-600">
            Failed to load dashboard data. Please try refreshing the page.
          </p>
          <button
            onClick={() => window.location.reload()}
            className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition"
          >
            Refresh Page
          </button>
        </div>
      </div>
    );
  }

  // Additional safety check - should not reach here due to loading check above
  if (!userData) {
    return null;
  }

  const { userRole, dashboardStats } = userData;

  // Don't render dashboard if employer needs onboarding
  if (
    userRole === UserRole.EMPLOYER &&
    userData.company &&
    userData.company.onboardingRequired === true
  ) {
    // Return null to prevent flash of dashboard before redirect
    return null;
  }
  
  // Don't render dashboard if job seeker/graduate needs onboarding
  if (
    (userRole === UserRole.JOB_SEEKER || userRole === UserRole.GRADUATE) &&
    userData.profile &&
    !userData.profile.hasCompletedOnboarding
  ) {
    // Return null to prevent flash of dashboard before redirect
    return null;
  }

  // Default status breakdowns
  const defaultEmployerStatusBreakdown = {
    new: 0,
    matched: 0,
    open: 0,
    contacted: 0,
    interested: 0,
    notInterested: 0,
    interviewing: 0,
    hired: 0,
    rejected: 0,
  };

  const defaultJobSeekerStatusBreakdown = {
    new: 0,
    matched: 0,
    contacted: 0,
    interested: 0,
    notInterested: 0,
    interviewing: 0,
    hired: 0,
    rejected: 0,
    withdrawn: 0,
    culturalFitAnswered: 0,
  };

  const defaultVideoJDByStatus = {
    SCRIPT_GENERATED: 0,
    GENERATING: 0,
    COMPLETED: 0,
    PENDING: 0,
  };

  // Prepare employer stats with default values
  const employerStats = {
    jobs: dashboardStats?.jobs || {
      total: 0,
      statusBreakdown: defaultEmployerStatusBreakdown,
      recentJobs: [],
      newJobs: { daily: 0, weekly: 0, monthly: 0, yearly: 0 },
      activeJobs: { daily: 0, weekly: 0, monthly: 0, yearly: 0 },
      closedJobs: { daily: 0, weekly: 0, monthly: 0, yearly: 0 },
      jobTrends: [],
    },
    candidates: dashboardStats?.candidates || {
      total: 0,
      matched: 0,
      culturalFitAnswered: 0,
      byStatus: {},
      recentMatches: [],
      newCandidates: { daily: 0, weekly: 0, monthly: 0, yearly: 0 },
      uploadedCVs: { daily: 0, weekly: 0, monthly: 0, yearly: 0 },
      appliedCandidates: { daily: 0, weekly: 0, monthly: 0, yearly: 0 },
      bySource: {},
      candidateTrends: [],
    },
    videoJDs: dashboardStats?.videoJDs || {
      total: 0,
      completed: 0,
      pending: 0,
      byStatus: defaultVideoJDByStatus,
      recentVideos: [],
    },
    notifications: dashboardStats?.notifications || {
      total: 0,
      unread: 0,
      today: 0,
      byType: {},
      recent: [],
    },
    culturalFit: dashboardStats?.culturalFit || {
      totalAnswered: 0,
      averageMatchScore: 0,
      recentResponses: [],
      jobBreakdown: [],
    },
    metrics: dashboardStats?.metrics || {
      totalJobSeekers: 0,
      totalCandidates: 0,
      totalJobs: 0,
      totalApplications: 0,
      totalHires: 0,
      totalUploads: 0,
      applicationToInterviewRate: 0,
      interviewToHireRate: 0,
      overallConversionRate: 0,
      averageTimeToHire: 0,
      averageTimeToInterview: 0,
      activeJobSeekers: { daily: 0, weekly: 0, monthly: 0, yearly: 0 },
      activeJobs: { daily: 0, weekly: 0, monthly: 0, yearly: 0 },
    },
    // Include subscription data from the stats
    subscription: dashboardStats?.subscription || null,
  };

  // Prepare job seeker stats with default values
  const jobSeekerStats = {
    applications: dashboardStats?.applications || {
      total: 0,
      statusBreakdown: defaultJobSeekerStatusBreakdown,
      recentApplications: [],
    },
    matchedJobs: dashboardStats?.matchedJobs || {
      total: 0,
      matchedThisWeek: 0,
      byIndustry: {},
      recentMatches: [],
    },
    notifications: dashboardStats?.notifications || {
      total: 0,
      unread: 0,
      today: 0,
      byType: {},
      recent: [],
    },
  };

  // Render appropriate dashboard based on user role (now authoritative from backend)
  return (
    <div className="w-full">
      {userRole === UserRole.EMPLOYER || userRole === UserRole.ADMIN ? (
        <EmployerDashboard stats={employerStats} loading={false} />
      ) : userRole === UserRole.JOB_SEEKER ? (
        <JobSeekerDashboard stats={jobSeekerStats} loading={isLoading} />
      ) : userRole === UserRole.GRADUATE ? (
        <GraduateDashboard stats={dashboardStats as any} loading={isLoading} />
      ) : (
        <div className="flex items-center justify-center min-h-[80vh]">
          <div className="text-center">
            <h2 className="text-xl font-semibold text-gray-600 mb-2">Invalid User Role</h2>
            <p className="text-gray-500">
              Unable to determine your dashboard type. Please contact support.
            </p>
          </div>
        </div>
      )}
    </div>
  );
};

export default Dashboard;
