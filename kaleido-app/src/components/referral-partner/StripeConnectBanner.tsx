'use client';

import { But<PERSON> } from '@/components/ui/button';
import { CreditCard } from 'lucide-react';

interface StripeConnectBannerProps {
  isConnected: boolean;
  isLoading: boolean;
  onConnect: () => void;
}

export default function StripeConnectBanner({
  isConnected,
  isLoading,
  onConnect,
}: StripeConnectBannerProps) {
  if (isConnected) {
    return null; // Don't show banner if already connected
  }

  return (
    <div className="bg-gradient-to-r from-purple-600/20 to-pink-600/20 border border-purple-500/30 rounded-lg p-6 mb-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <div className="w-12 h-12 bg-gradient-to-r from-purple-600 to-pink-600 rounded-full flex items-center justify-center">
            <CreditCard className="w-6 h-6 text-white" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-white mb-1">Connect Stripe to Get Paid</h3>
            <p className="text-sm text-white/70">
              Set up payouts to receive your earnings directly to your bank account
            </p>
          </div>
        </div>
        <Button
          onClick={onConnect}
          disabled={isLoading}
          className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 px-6"
        >
          {isLoading ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
              Connecting...
            </>
          ) : (
            <>
              <CreditCard className="w-4 h-4 mr-2" />
              Connect Stripe
            </>
          )}
        </Button>
      </div>
    </div>
  );
}
