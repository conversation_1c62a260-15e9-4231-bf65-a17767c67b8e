import { getSession, getAccessToken } from '@auth0/nextjs-auth0';
import { NextApiRequest, NextApiResponse } from 'next';
import axios from 'axios';

const BACKEND_API_URL = process.env.NEXT_PUBLIC_API_URL_BASE || 'http://localhost:8080/api';

export default async function userRoles(req: NextApiRequest, res: NextApiResponse) {
  try {
    // Get the session using Auth0's getSession
    const session = await getSession(req, res);

    if (!session?.user) {
      return res.status(401).json({
        error: 'Not authenticated',
        code: 'NO_SESSION',
      });
    }

    // Get the access token for server-side API calls
    const { accessToken } = await getAccessToken(req, res);

    // Fetch user roles from backend using direct axios call with auth header
    try {
      const response = await axios.get(
        `${BACKEND_API_URL}/roles/${session.user.sub || session.user.user_id}`,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
            'Content-Type': 'application/json',
          },
        }
      );

      // Handle null or undefined response
      if (!response || !response.data) {
        console.warn('No response from /roles endpoint');
        return res.json({
          primaryRole: session.user.roles?.[0] || null,
          additionalRoles: [],
          activeRole: session.user.roles?.[0] || null,
        });
      }

      // axios returns data wrapped in response.data
      const roleData = response.data;

      return res.json({
        primaryRole: roleData.role || session.user.roles?.[0] || null,
        additionalRoles: roleData.additionalRoles || [],
        activeRole: roleData.activeRole || roleData.role || session.user.roles?.[0] || null,
      });
    } catch (error: any) {
      console.error('Error fetching user roles:', error);

      // If backend returns 404, user might not have a role entry yet
      if (error.response?.status === 404 || error.status === 404) {
        return res.json({
          primaryRole: session.user.roles?.[0] || null,
          additionalRoles: [],
          activeRole: session.user.roles?.[0] || null,
        });
      }

      // If circuit breaker or rate limiting error, return cached JWT roles
      if (error.message?.includes('Circuit breaker') || error.message?.includes('Too many calls')) {
        console.warn('Rate limited or circuit breaker active, using JWT roles');
        return res.json({
          primaryRole: session.user.roles?.[0] || null,
          additionalRoles: [],
          activeRole: session.user.roles?.[0] || null,
        });
      }

      return res.status(error.response?.status || 500).json({
        error: 'Failed to fetch user roles',
        code: 'BACKEND_ERROR',
      });
    }
  } catch (error: any) {
    console.error('Error in user-roles endpoint:', error);
    res.status(500).json({
      error: error instanceof Error ? error.message : 'Internal server error',
      code: 'INTERNAL_ERROR',
    });
  }
}
