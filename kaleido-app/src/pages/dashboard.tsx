'use client';

import React, { useEffect, useState } from 'react';

import RoleBasedRedirectHandler from '@/components/Auth/RoleBasedRedirectHandler';
import { Dashboard } from '@/components/Dashboard';
import { JobSeekerSetupSlider } from '@/components/JobSeeker/JobSeekerSetupSlider';
import { IncompleteProfileAlert } from '@/components/JobSeeker/components/IncompleteProfileAlert';
import { ProfileDataCollectionModal } from '@/components/JobSeeker/components/ProfileDataCollectionModal';
import { showToast } from '@/components/Toaster';
import { EmployerOnboardingGuard } from '@/components/Dashboard/EmployerOnboardingGuard';
import { JobSeekerOnboardingGuard } from '@/components/Dashboard/JobSeekerOnboardingGuard';
import AppLayout from '@/components/steps/layout/AppLayout';
import { JobSearchProvider } from '@/contexts/jobSearch/JobSearchContext';
import { useAuthSync } from '@/hooks/useAuthSync';
import { useAuthStore } from '@/stores/authStore';
import useEnhancedUserData from '@/hooks/useEnhancedUserData';
import useProactiveProfileValidation from '@/hooks/useProactiveProfileValidation';
import { useProfileCompletion } from '@/hooks/useProfileCompletion';
import { UserRole } from '@/shared/types';
import { useOnboardingStore } from '@/stores/onboardingStore';
import { useRouter } from 'next/router';

const DashboardContent: React.FC = () => {
  const router = useRouter();

  // Use auth sync and store
  useAuthSync();
  const { isAuthenticated, isLoading, session } = useAuthStore();
  const { userData } = useEnhancedUserData();
  const [linkedInData, setLinkedInData] = useState<any>(null);

  // Use Zustand store for onboarding management
  const {
    profile,
    isLoading: isLoadingProfile,
    shouldShowSetupSlider,
    hasCheckedOnboarding,
    fetchProfile,
    setShouldShowSetupSlider,
    markOnboardingComplete,
    setUserRole,
  } = useOnboardingStore();

  // Profile completion modal handling
  const { isModalOpen, missingFields, closeModal, handleComplete } = useProfileCompletion();

  // Proactive profile validation alert
  const {
    shouldShowAlert,
    validation: alertValidation,
    dismissAlert,
  } = useProactiveProfileValidation([UserRole.JOB_SEEKER], {
    checkOnMount: true,
    minCompletionThreshold: 60, // Show alert if completion < 60%
    dismissedStorageKey: 'dashboardProfileAlert',
  });

  // Extract query parameters
  const { role } = router.query;

  // Extract LinkedIn data when user loads
  useEffect(() => {
    if (session?.user?.linkedInProfile) {
      setLinkedInData(session.user.linkedInProfile);
    }
  }, [session?.user]);

  // Set user role in onboarding store
  useEffect(() => {
    if (userData?.userRole) {
      setUserRole(userData.userRole);
    }
  }, [userData?.userRole, setUserRole]);

  // Show loading state until we have user data
  const isDataReady = isAuthenticated && !isLoading && userData !== null && userData !== undefined;

  // Fetch profile and check onboarding status for job seekers
  useEffect(() => {
    if (!isAuthenticated || isLoading || !userData) return;

    // If the role is explicitly set to job-seeker or the user's role is job-seeker
    const isJobSeekerRole =
      role === UserRole.JOB_SEEKER || userData?.userRole === UserRole.JOB_SEEKER;

    if (isJobSeekerRole && !hasCheckedOnboarding) {
      fetchProfile();
    }
  }, [isAuthenticated, isLoading, role, userData?.userRole, hasCheckedOnboarding, fetchProfile]);

  const handleOnboardingComplete = async () => {
    try {
      // Mark onboarding as complete in the store
      markOnboardingComplete();

      // Set onboarding completion cookie for middleware
      if (session?.user?.sub) {
        const expiryDate = new Date();
        expiryDate.setFullYear(expiryDate.getFullYear() + 1); // 1 year expiry
        document.cookie = `onboardingComplete_${session.user.sub}=true; expires=${expiryDate.toUTCString()}; path=/; SameSite=Lax; Secure`;
      }

      // Refresh the profile data
      await fetchProfile();

      // Show a success message for completing the profile
      showToast({
        message: 'Your profile has been successfully completed!',
        isSuccess: true,
      });

      // Force a full page refresh to the job seeker dashboard
      window.location.href = '/dashboard';
    } catch (error) {
      console.error('Error completing onboarding:', error);
      showToast({
        message: 'Profile completed, but there was an issue refreshing. Please refresh the page.',
        isSuccess: false,
      });
      // Still redirect even if there's an error
      window.location.href = '/dashboard';
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50/50 to-purple-50/30">
      {/* Handle role-based redirects after Auth0 authentication */}
      <RoleBasedRedirectHandler />
      <AppLayout isLoading={!isDataReady || isLoadingProfile}>
        {!isDataReady ? null : userData.userRole === UserRole.EMPLOYER ? (
          <EmployerOnboardingGuard userData={userData}>
            <Dashboard />
          </EmployerOnboardingGuard>
        ) : userData.userRole === UserRole.JOB_SEEKER ? (
          <JobSeekerOnboardingGuard 
            userData={userData}
            onboardingData={{ shouldShowSetupSlider, hasCheckedOnboarding }}
          >
            {shouldShowSetupSlider ? (
          <JobSeekerSetupSlider
            onComplete={handleOnboardingComplete}
            onClose={() => {
              // Set onboarding completion cookie when user closes from dashboard
              if (session?.user?.sub) {
                const expiryDate = new Date();
                expiryDate.setFullYear(expiryDate.getFullYear() + 1); // 1 year expiry
                document.cookie = `onboardingComplete_${session.user.sub}=true; expires=${expiryDate.toUTCString()}; path=/; SameSite=Lax; Secure`;
              }
              setShouldShowSetupSlider(false);
              router.push('/dashboard');
            }}
            initialData={
              {
                ...(profile || {}),
                linkedInProfile: linkedInData,
              } as any
            }
            initialStep="resume"
          />
        ) : (
              <Dashboard />
            )}
          </JobSeekerOnboardingGuard>
        ) : (
          <Dashboard />
        )}

        {/* Profile data collection modal for missing LinkedIn data */}
        <ProfileDataCollectionModal
          isOpen={isModalOpen}
          onClose={closeModal}
          onComplete={handleComplete}
          missingFields={missingFields}
        />

        {/* Proactive profile completion alert - only show for job seekers during onboarding */}
        {shouldShowAlert && alertValidation && userData?.userRole === UserRole.JOB_SEEKER && (
          <IncompleteProfileAlert validation={alertValidation} onClose={dismissAlert} />
        )}
      </AppLayout>
    </div>
  );
};

const DashboardPage: React.FC = () => {
  return (
    <JobSearchProvider>
      <DashboardContent />
    </JobSearchProvider>
  );
};

export default DashboardPage;
