import React, { createContext, useContext, useState, useCallback, ReactNode } from 'react';
import { ICandidate } from '@/entities/interfaces';

interface CandidateSelectionContextType {
  selectedCandidates: ICandidate[];
  isSelectionMode: boolean;
  addCandidate: (candidate: ICandidate) => void;
  removeCandidate: (candidateId: string) => void;
  toggleCandidate: (candidate: ICandidate) => void;
  clearSelection: () => void;
  isSelected: (candidateId: string) => boolean;
  setSelectionMode: (enabled: boolean) => void;
  selectedCount: number;
}

const CandidateSelectionContext = createContext<CandidateSelectionContextType | undefined>(
  undefined
);

export const CandidateSelectionProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [selectedCandidates, setSelectedCandidates] = useState<ICandidate[]>([]);
  const [isSelectionMode, setIsSelectionMode] = useState(false);

  const addCandidate = useCallback((candidate: ICandidate) => {
    setSelectedCandidates(prev => {
      // Prevent duplicates
      if (prev.some(c => c.id === candidate.id)) {
        return prev;
      }
      // Limit to 3 candidates maximum
      if (prev.length >= 3) {
        return prev;
      }
      return [...prev, candidate];
    });
  }, []);

  const removeCandidate = useCallback((candidateId: string) => {
    setSelectedCandidates(prev => prev.filter(c => c.id !== candidateId));
  }, []);

  const toggleCandidate = useCallback((candidate: ICandidate) => {
    setSelectedCandidates(prev => {
      const exists = prev.some(c => c.id === candidate.id);
      if (exists) {
        return prev.filter(c => c.id !== candidate.id);
      } else {
        // Limit to 3 candidates maximum
        if (prev.length >= 3) {
          return prev;
        }
        return [...prev, candidate];
      }
    });
  }, []);

  const clearSelection = useCallback(() => {
    setSelectedCandidates([]);
    setIsSelectionMode(false);
  }, []);

  const isSelected = useCallback(
    (candidateId: string) => {
      return selectedCandidates.some(c => c.id === candidateId);
    },
    [selectedCandidates]
  );

  const setSelectionModeHandler = useCallback(
    (enabled: boolean) => {
      setIsSelectionMode(enabled);
      if (!enabled) {
        clearSelection();
      }
    },
    [clearSelection]
  );

  const value: CandidateSelectionContextType = {
    selectedCandidates,
    isSelectionMode,
    addCandidate,
    removeCandidate,
    toggleCandidate,
    clearSelection,
    isSelected,
    setSelectionMode: setSelectionModeHandler,
    selectedCount: selectedCandidates.length,
  };

  return (
    <CandidateSelectionContext.Provider value={value}>
      {children}
    </CandidateSelectionContext.Provider>
  );
};

export const useCandidateSelection = () => {
  const context = useContext(CandidateSelectionContext);
  if (!context) {
    throw new Error('useCandidateSelection must be used within a CandidateSelectionProvider');
  }
  return context;
};
