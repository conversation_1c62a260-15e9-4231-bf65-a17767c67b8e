# Dual-Role Referral System Implementation

## Overview
This document provides a comprehensive breakdown of the dual-role referral system implementation that allows existing job seekers and companies to participate in the referral program without creating separate accounts.

## Implementation Summary

### Core Concept
- Users maintain their primary role (JOB_SEEKER, EMPLOYER, etc.)
- Can activate additional roles (REFERRAL_PARTNER) without switching accounts
- Navigation shows combined menu items from all active roles
- No role switching required - users see all available features

## Files Modified

### Backend Files

#### 1. Entity Updates
```bash
# Modified entities to support referral connections
src/modules/roles/entities/user-role.entity.ts
src/modules/job-seeker/entities/job-seeker.entity.ts
src/modules/company/entities/company.entity.ts
src/modules/referral/entities/referral-partner.entity.ts
```

**Changes:**
- Added `additionalRoles: UserRole[]` to UserRoleEntity
- Added `referralPartnerId`, `isReferralPartner` to JobSeeker entity
- Added `referralPartnerId` to Company entity
- Added reverse relationships `jobSeeker` and `company` to ReferralPartner

#### 2. Services
```bash
# New and modified services
src/modules/referral/services/referral-activation.service.ts  # NEW
src/modules/referral/services/index.ts
src/modules/roles/roles.service.ts
src/auth/auth.service.ts
```

**Key Features:**
- `ReferralActivationService`: Handles activation/deactivation of referral program
- Updated `RolesService` with `addAdditionalRole()` and `removeAdditionalRole()`
- Modified `AuthService.hasRole()` to check both JWT and database roles

#### 3. Controllers
```bash
# New and modified controllers
src/modules/referral/controllers/referral-activation.controller.ts  # NEW
src/modules/referral/controllers/referral-partner.controller.ts
src/modules/referral/controllers/index.ts
src/modules/roles/roles.controller.ts
```

**Endpoints Added:**
- `POST /referral/activation/job-seeker` - Activate for job seekers
- `POST /referral/activation/company` - Activate for companies
- `DELETE /referral/activation/deactivate` - Deactivate referral program
- `GET /referral-partners/check-status` - Check referral status (no role required)

#### 4. Module Configuration
```bash
src/modules/referral/referral.module.ts
```

**Changes:**
- Added `ReferralActivationService` and `ReferralActivationController`
- Added `JobSeekerModule` and `CompanyModule` imports

#### 5. Database Migration
```bash
src/migrations/1754350000000-AddDualRoleSupport.ts  # NEW
src/scripts/mark-migrations-run.ts  # NEW (utility script)
```

**Schema Changes:**
- Added columns to support dual-role functionality
- Created relationships between entities

### Frontend Files

#### 1. Navigation System
```bash
src/lib/navigation.ts
src/components/steps/layout/BaseLayout.tsx
```

**Changes:**
- Added `getNavItemsByRoles()` function to filter nav by multiple roles
- Updated BaseLayout to use additional roles from `useUserRoles` hook

#### 2. User Role Management
```bash
# New hooks and API endpoints
src/hooks/useUserRoles.ts  # NEW
src/pages/api/auth/user-roles.ts  # NEW
```

**Features:**
- `useUserRoles` hook provides access to all user roles
- Includes caching to prevent excessive API calls
- Fallback to JWT roles on error

#### 3. Referral Components
```bash
# New components
src/components/shared/ReferralProgramBanner.tsx  # NEW
src/components/job-seeker/referral/ReferralProgramCard.tsx  # NEW
src/components/company/referral/PartnerProgramSettings.tsx  # NEW
```

**Features:**
- Banner shows on dashboards for non-partners
- Activation cards for both job seekers and companies
- Settings component for managing referral program

#### 4. Dashboard Integration
```bash
src/components/Dashboard/JobSeeker/JobSeekerDashboard.tsx
src/components/Dashboard/Employer/EmployerDashboard.tsx
```

**Changes:**
- Added `<ReferralProgramBanner>` to both dashboards
- Banners prefill onboarding with appropriate data

#### 5. API Services
```bash
src/app/referral-partner/services/referralApi.ts
src/app/referral-partner/services/referralActivationApi.ts  # NEW
```

**Features:**
- API methods for activation/deactivation
- Caching for status checks (5 minutes)
- Error handling to prevent infinite loops

#### 6. Onboarding Integration
```bash
src/app/referral-partner/onboarding/page.tsx
```

**Changes:**
- Accepts query parameters for prefilled data
- Shows indicator when form is prefilled

## Current Architecture

### Authentication Flow
1. User logs in with primary role (JWT)
2. Database stores additional roles
3. `hasRole()` checks both JWT and database
4. Navigation shows all role-based items

### Activation Flow
1. User sees referral banner on dashboard
2. Clicks to activate → goes to onboarding
3. Form prefilled based on user type
4. On completion → REFERRAL_PARTNER added to additionalRoles
5. User redirected to referral dashboard

### Data Model
```typescript
UserRoleEntity {
  role: UserRole;           // Primary role
  additionalRoles: UserRole[]; // Additional roles
  activeRole?: UserRole;    // For future role switching
}

JobSeeker/Company {
  referralPartnerId?: string;
  isReferralPartner?: boolean;
  referralPartner?: ReferralPartner;
}

ReferralPartner {
  jobSeekerId?: string;
  companyId?: string;
  jobSeeker?: JobSeeker;
  company?: Company;
}
```

## Identified Gaps & Recommendations

### 1. Testing Coverage
**Gap:** No unit tests for new functionality

**Recommendation:**
```typescript
// Add tests for:
- ReferralActivationService
- ReferralActivationController
- useUserRoles hook
- ReferralProgramBanner component
```

### 2. Role Synchronization
**Gap:** Roles in JWT may get out of sync with database

**Recommendation:**
- Add background job to sync roles periodically
- Or refresh JWT when additionalRoles change
- Consider webhook from backend to Auth0

### 3. Role Switching UI
**Gap:** `activeRole` field exists but not used

**Recommendation:**
- Add role switcher component if needed
- Allow users to switch context while maintaining all permissions
- Update navigation to highlight active role

### 4. Permissions Granularity
**Gap:** Role checking is binary (has/doesn't have)

**Recommendation:**
```typescript
// Add permission-based checks
interface Permission {
  resource: string;
  action: string;
}

// Check specific permissions
canUserPerform(user, 'referral', 'create')
```

### 5. Analytics & Monitoring
**Gap:** No tracking of dual-role usage

**Recommendation:**
- Track role activation events
- Monitor API performance (caching effectiveness)
- Add dashboards for dual-role adoption

### 6. Error Recovery
**Gap:** Limited error recovery mechanisms

**Recommendation:**
- Add retry logic with exponential backoff
- Implement offline support for role checking
- Add user-friendly error messages

### 7. Migration Rollback
**Gap:** No rollback strategy for dual-role changes

**Recommendation:**
- Create rollback migration
- Add feature flags for gradual rollout
- Implement data backup before activation

## API Endpoints Summary

### Backend Endpoints
```bash
# Role Management
GET  /roles/:clientId          # Get user roles
POST /roles/sync-from-storage  # Sync role from frontend

# Referral Activation
POST   /referral/activation/job-seeker  # Activate for job seeker
POST   /referral/activation/company     # Activate for company  
DELETE /referral/activation/deactivate  # Deactivate

# Referral Partner
GET /referral-partners/check-status  # Check status (no role required)
GET /referral-partners/me           # Get profile (requires role)
```

### Frontend API Routes
```bash
GET /api/auth/user-roles  # Get all user roles with caching
```

## Performance Optimizations

### 1. Caching Strategy
- User roles: 10 minutes (frontend)
- Referral status: 5 minutes (frontend)
- LocalStorage fallback on errors

### 2. Request Deduplication
- Prevents multiple simultaneous requests
- Circuit breaker pattern for rate limiting
- Graceful degradation on backend errors

### 3. Lazy Loading
- Referral components only load when needed
- Dynamic imports for referral pages
- Conditional rendering based on roles

## Security Considerations

### 1. Role Validation
- Backend validates all role changes
- Frontend role checks are for UX only
- JWT + database dual validation

### 2. Data Access
- Row-level security based on clientId
- Referral partners can only see own data
- Admin override capabilities

### 3. Audit Trail
- All role changes should be logged
- Track who activated referral program
- Monitor for suspicious patterns

## Next Steps

### Immediate (Priority 1)
1. Add comprehensive test coverage
2. Implement error tracking (Sentry)
3. Add feature flags for rollout

### Short-term (Priority 2)
1. Build admin dashboard for role management
2. Add analytics for referral program adoption
3. Implement role synchronization with Auth0

### Long-term (Priority 3)
1. Mobile app support for dual roles
2. Advanced permission system
3. Role delegation features

## Maintenance Notes

### Database
- Run migrations on deployment
- Monitor for migration conflicts
- Keep additionalRoles array small

### Caching
- Clear caches on role changes
- Monitor cache hit rates
- Adjust TTL based on usage

### Monitoring
- Track API response times
- Monitor error rates
- Alert on role sync failures

## Conclusion

The dual-role referral system successfully allows users to maintain their primary identity while gaining additional capabilities. The implementation focuses on:

1. **Simplicity**: No role switching required
2. **Performance**: Aggressive caching prevents loops
3. **Flexibility**: Easy to add more additional roles
4. **Security**: Dual validation (JWT + database)

The system is production-ready but would benefit from additional testing, monitoring, and gradual rollout with feature flags.