import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddDualRoleSupport1754350000000 implements MigrationInterface {
  name = 'AddDualRoleSupport1754350000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add additional_roles to user_roles table
    await queryRunner.query(
      `ALTER TABLE "user_roles" ADD "additionalRoles" text array DEFAULT '{}'`,
    );

    // Add referral partner fields to job_seekers
    await queryRunner.query(`ALTER TABLE "job_seekers" ADD "referralPartnerId" uuid`);
    await queryRunner.query(
      `ALTER TABLE "job_seekers" ADD "isReferralPartner" boolean DEFAULT false`,
    );
    await queryRunner.query(
      `ALTER TABLE "job_seekers" ADD CONSTRAINT "UQ_job_seekers_referralPartnerId" UNIQUE ("referralPartnerId")`,
    );
    await queryRunner.query(
      `ALTER TABLE "job_seekers" ADD CONSTRAINT "FK_job_seekers_referralPartnerId" FOREIGN KEY ("referralPartnerId") REFERENCES "referral_partners"("id") ON DELETE SET NULL ON UPDATE CASCADE`,
    );

    // Add referral partner fields to companies
    await queryRunner.query(`ALTER TABLE "companies" ADD "referralPartnerId" uuid`);
    await queryRunner.query(
      `ALTER TABLE "companies" ADD CONSTRAINT "UQ_companies_referralPartnerId" UNIQUE ("referralPartnerId")`,
    );
    await queryRunner.query(
      `ALTER TABLE "companies" ADD CONSTRAINT "FK_companies_referralPartnerId" FOREIGN KEY ("referralPartnerId") REFERENCES "referral_partners"("id") ON DELETE SET NULL ON UPDATE CASCADE`,
    );

    // Add reverse connections to referral_partners
    await queryRunner.query(`ALTER TABLE "referral_partners" ADD "jobSeekerId" uuid`);
    await queryRunner.query(
      `ALTER TABLE "referral_partners" ADD CONSTRAINT "UQ_referral_partners_jobSeekerId" UNIQUE ("jobSeekerId")`,
    );
    await queryRunner.query(
      `ALTER TABLE "referral_partners" ADD CONSTRAINT "FK_referral_partners_jobSeekerId" FOREIGN KEY ("jobSeekerId") REFERENCES "job_seekers"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );

    // Create indexes for performance
    await queryRunner.query(
      `CREATE INDEX "IDX_job_seekers_referralPartnerId" ON "job_seekers" ("referralPartnerId")`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_companies_referralPartnerId" ON "companies" ("referralPartnerId")`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_referral_partners_jobSeekerId" ON "referral_partners" ("jobSeekerId")`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop indexes
    await queryRunner.query(`DROP INDEX "IDX_referral_partners_jobSeekerId"`);
    await queryRunner.query(`DROP INDEX "IDX_companies_referralPartnerId"`);
    await queryRunner.query(`DROP INDEX "IDX_job_seekers_referralPartnerId"`);

    // Remove reverse connections from referral_partners
    await queryRunner.query(
      `ALTER TABLE "referral_partners" DROP CONSTRAINT "FK_referral_partners_jobSeekerId"`,
    );
    await queryRunner.query(
      `ALTER TABLE "referral_partners" DROP CONSTRAINT "UQ_referral_partners_jobSeekerId"`,
    );
    await queryRunner.query(`ALTER TABLE "referral_partners" DROP COLUMN "jobSeekerId"`);

    // Remove referral partner fields from companies
    await queryRunner.query(
      `ALTER TABLE "companies" DROP CONSTRAINT "FK_companies_referralPartnerId"`,
    );
    await queryRunner.query(
      `ALTER TABLE "companies" DROP CONSTRAINT "UQ_companies_referralPartnerId"`,
    );
    await queryRunner.query(`ALTER TABLE "companies" DROP COLUMN "referralPartnerId"`);

    // Remove referral partner fields from job_seekers
    await queryRunner.query(
      `ALTER TABLE "job_seekers" DROP CONSTRAINT "FK_job_seekers_referralPartnerId"`,
    );
    await queryRunner.query(
      `ALTER TABLE "job_seekers" DROP CONSTRAINT "UQ_job_seekers_referralPartnerId"`,
    );
    await queryRunner.query(`ALTER TABLE "job_seekers" DROP COLUMN "isReferralPartner"`);
    await queryRunner.query(`ALTER TABLE "job_seekers" DROP COLUMN "referralPartnerId"`);

    // Remove additional_roles from user_roles
    await queryRunner.query(`ALTER TABLE "user_roles" DROP COLUMN "additionalRoles"`);
  }
}
