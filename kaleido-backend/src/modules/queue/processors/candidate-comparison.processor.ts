import { Process, Processor } from '@nestjs/bull';
import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Job } from 'bull';
import { Repository } from 'typeorm';

import { PROCESSOR_NAMES, QUEUE_NAMES } from '@/shared/constants/queue.constants';
import { CandidateComparison } from '@/modules/comparison/entities/candidate-comparison.entity';
import { CandidateComparisonService } from '@/modules/comparison/services/candidate-comparison.service';

interface ComparisonJobData {
  comparisonId: string;
  userId: string;
}

@Injectable()
@Processor(QUEUE_NAMES.CANDIDATE_COMPARISON)
export class CandidateComparisonProcessor {
  private readonly logger = new Logger(CandidateComparisonProcessor.name);

  constructor(
    @InjectRepository(CandidateComparison)
    private readonly comparisonRepository: Repository<CandidateComparison>,
    private readonly comparisonService: CandidateComparisonService,
  ) {}

  @Process(PROCESSOR_NAMES.PROCESS_COMPARISON)
  async processComparison(job: Job<ComparisonJobData>) {
    const { comparisonId, userId } = job.data;

    this.logger.log(`Starting comparison processing for ID: ${comparisonId}`);

    try {
      // Initial progress - Starting
      await job.progress(5);
      await job.log('Initializing comparison...');

      // Get the comparison entity
      const comparison = await this.comparisonRepository.findOne({
        where: { id: comparisonId, clientId: userId },
      });

      if (!comparison) {
        throw new Error(`Comparison ${comparisonId} not found`);
      }

      // Progress - Found comparison
      await job.progress(15);
      await job.log('Loading candidate data...');

      // Simulate more granular progress during processing
      await job.progress(25);
      await job.log('Analyzing candidate profiles...');

      // Small delay to show progress
      await new Promise((resolve) => setTimeout(resolve, 500));

      await job.progress(30);
      await job.log('Extracting candidate strengths...');

      await new Promise((resolve) => setTimeout(resolve, 400));

      await job.progress(40);
      await job.log('Comparing qualifications and experience...');

      // Start processing
      const processingPromise = this.comparisonService.processComparison(comparison);

      // Continue updating progress while processing
      await job.progress(50);
      await job.log('Analyzing technical skills...');

      await new Promise((resolve) => setTimeout(resolve, 500));

      await job.progress(60);
      await job.log('Evaluating skills and competencies...');

      // Wait for actual processing to complete
      const result = await processingPromise;

      await job.progress(70);
      await job.log('Comparing cultural fit...');

      await new Promise((resolve) => setTimeout(resolve, 300));

      await job.progress(75);
      await job.log('Generating comparison insights...');

      // Update the comparison status to completed
      await this.comparisonRepository.update(comparisonId, {
        status: 'completed',
        comparisonResults: result,
        updatedAt: new Date(),
      });

      await job.progress(90);
      await job.log('Finalizing results...');

      // Final delay before completion
      await new Promise((resolve) => setTimeout(resolve, 200));

      await job.progress(100);
      await job.log('Comparison completed successfully!');

      this.logger.log(`Comparison ${comparisonId} completed successfully`);

      return {
        comparisonId,
        jobId: comparisonId, // Include the comparison entity ID for frontend navigation
        status: 'completed',
        result,
      };
    } catch (error) {
      this.logger.error(`Error processing comparison ${comparisonId}:`, error);

      // Update the comparison status to failed
      await this.comparisonRepository.update(comparisonId, {
        status: 'failed',
        updatedAt: new Date(),
      });

      throw error;
    }
  }
}
