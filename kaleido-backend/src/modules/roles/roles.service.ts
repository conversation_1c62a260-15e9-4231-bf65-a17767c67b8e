import { Repository } from 'typeorm';

import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';

import { Auth0ManagementService } from '../../auth/auth0-management.service';
import { UserRole } from '../../common/enums/role.enum';
import { CreateUserRoleDto } from './dto/create-user-role.dto';
import { UserRoleEntity } from './entities/user-role.entity';

@Injectable()
export class RolesService {
  constructor(
    @InjectRepository(UserRoleEntity)
    private readonly userRoleRepository: Repository<UserRoleEntity>,
    private readonly auth0ManagementService: Auth0ManagementService,
  ) {}

  async create(createUserRoleDto: CreateUserRoleDto): Promise<UserRoleEntity> {
    const role = await this.userRoleRepository.findOne({
      where: { clientId: createUserRoleDto.clientId },
    });
    if (role) {
      return role;
    }
    const userRole = this.userRoleRepository.create(createUserRoleDto);
    return await this.userRoleRepository.save(userRole);
  }

  async findByClientId(clientId: string): Promise<UserRoleEntity | null> {
    try {
      const userRole = await this.userRoleRepository.findOne({ where: { clientId } });
      if (!userRole) {
        return null;
      }
      return userRole;
    } catch (error) {
      console.error(`RolesService: Error looking up role for ${clientId}:`, error);
      return null;
    }
  }

  async updateRole(clientId: string, role: UserRole): Promise<UserRoleEntity> {
    const userRole = await this.findByClientId(clientId);
    if (!userRole) {
      console.error(`RolesService: User role not found for clientId: ${clientId}`);
      throw new NotFoundException(`User role not found for clientId: ${clientId}`);
    }

    // Check if this is an admin user - don't allow downgrading admin roles unless explicitly requested
    if (userRole.role === UserRole.ADMIN && role !== UserRole.ADMIN) {
      return userRole; // Return existing admin role without changing it
    }

    // Always update to the new role for non-admin users or admin-to-admin updates
    const oldRole = userRole.role;
    userRole.role = role;
    const updatedRole = await this.userRoleRepository.save(userRole);

    // Update Auth0 user metadata with the new role
    try {
      await this.auth0ManagementService.updateUserRole(clientId, role);
    } catch (error) {
      console.error(`RolesService: Failed to update Auth0 role for ${clientId}:`, error);
      // Don't fail the entire operation if Auth0 update fails
    }

    return updatedRole;
  }

  /**
   * Force update role - bypasses admin protection for explicit admin role changes
   * Use this method only when you explicitly want to change an admin role
   */
  async forceUpdateRole(clientId: string, role: UserRole): Promise<UserRoleEntity> {
    const userRole = await this.findByClientId(clientId);
    if (!userRole) {
      console.error(`RolesService: User role not found for clientId: ${clientId}`);
      throw new NotFoundException(`User role not found for clientId: ${clientId}`);
    }

    const oldRole = userRole.role;
    userRole.role = role;
    const updatedRole = await this.userRoleRepository.save(userRole);

    // Update Auth0 user metadata with the new role
    try {
      await this.auth0ManagementService.updateUserRole(clientId, role);
    } catch (error) {
      console.error(`RolesService: Failed to update Auth0 role for ${clientId}:`, error);
      // Don't fail the entire operation if Auth0 update fails
    }

    return updatedRole;
  }

  /**
   * Add an additional role to a user
   */
  async addAdditionalRole(clientId: string, role: UserRole): Promise<UserRoleEntity> {
    const userRole = await this.findByClientId(clientId);
    if (!userRole) {
      console.error(`RolesService: User role not found for clientId: ${clientId}`);
      throw new NotFoundException(`User role not found for clientId: ${clientId}`);
    }

    // Initialize additionalRoles if it doesn't exist
    if (!userRole.additionalRoles) {
      userRole.additionalRoles = [];
    }

    // Add role if it's not already present
    if (!userRole.additionalRoles.includes(role)) {
      userRole.additionalRoles.push(role);
      return await this.userRoleRepository.save(userRole);
    }

    return userRole;
  }

  /**
   * Remove an additional role from a user
   */
  async removeAdditionalRole(clientId: string, role: UserRole): Promise<UserRoleEntity> {
    const userRole = await this.findByClientId(clientId);
    if (!userRole) {
      console.error(`RolesService: User role not found for clientId: ${clientId}`);
      throw new NotFoundException(`User role not found for clientId: ${clientId}`);
    }

    if (userRole.additionalRoles) {
      userRole.additionalRoles = userRole.additionalRoles.filter((r) => r !== role);
      return await this.userRoleRepository.save(userRole);
    }

    return userRole;
  }
}
