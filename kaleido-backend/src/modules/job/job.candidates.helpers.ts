import * as crypto from 'crypto';
import { In, Repository } from 'typeorm';

import { ApplicationStatus, CandidateStatus } from '@/shared/types';
import { Candidate } from '@modules/candidate/entities/candidate.entity';
import { JobApplication } from '@modules/job-seeker/entities/job-application.entity';
import { JobSeeker } from '@modules/job-seeker/entities/job-seeker.entity';
import { JobSeekerService } from '@modules/job-seeker/job-seeker.service';
import { forwardRef, Inject, Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { ActivityHistory } from '@shared/entities/activity-history.entity';
import { ActivityType } from '@shared/types/activity.types';
import { determineCandidateTier, normalizeJobThresholds } from '@shared/utils/threshold.util';
import { getCandidateJobWhereClause } from '@shared/utils/candidate-job-query.util';

import { GraduateService } from '../graduate/graduate.service';
import { CandidateComparison } from '../comparison/entities/candidate-comparison.entity';
import { Job } from './entities/job.entity';
import { JobCrudUtils } from './job.crud.utils';
import { getJobApplicationStatusForCandidate } from './job.helper-2';
import { JobsResponse } from './jobs.types';

@Injectable()
export class JobCandidatesHelpers {
  constructor(
    @InjectRepository(Job)
    private readonly jobRepository: Repository<Job>,
    @InjectRepository(JobApplication)
    private readonly jobApplicationRepository: Repository<JobApplication>,
    @InjectRepository(JobSeeker)
    private readonly jobSeekerRepository: Repository<JobSeeker>,
    @Inject(forwardRef(() => JobSeekerService))
    private readonly jobSeekerService: JobSeekerService,
    private readonly graduatesService: GraduateService,
    private readonly jobCrudUtils: JobCrudUtils,
    @InjectRepository(Candidate)
    private readonly candidateRepository: Repository<Candidate>,
    @InjectRepository(CandidateComparison)
    private readonly comparisonRepository: Repository<CandidateComparison>,
  ) {}

  private async logCandidateActivity(
    candidateId: string,
    type: ActivityType,
    description: string,
    metadata?: Record<string, any>,
  ): Promise<void> {
    const candidate = await this.candidateRepository.findOne({
      where: { id: candidateId },
    });

    if (!candidate) {
      return; // Skip logging if candidate not found
    }

    const activity: ActivityHistory = {
      id: crypto.randomUUID(),
      type,
      description,
      timestamp: new Date(),
      metadata: metadata || {},
    };

    // Update candidate's activity history
    const activityHistory = candidate.activityHistory || [];
    activityHistory.push(activity);

    await this.candidateRepository.update(candidateId, {
      activityHistory,
    });

    // If this is a status change, also update the job application status
    if (type === ActivityType.STATUS_CHANGED && metadata?.jobId && metadata?.newStatus) {
      await this.updateJobApplicationStatus(
        metadata.jobId,
        candidate.jobSeekerId ?? '',
        metadata.newStatus,
      );
    }
  }

  async findJobWithCandidates(jobId: string): Promise<Job> {
    return this.jobCrudUtils.findJobWithCandidates(jobId);
  }

  async findCandidates(jobId: string) {
    /**
     * Optimized for MatchedCandidatesComposition performance:
     * - No pagination (frontend handles it)
     * - No videoResponses (loaded separately when candidate selected)
     * - No activityHistory (not used in candidates list view)
     * - Includes job thresholds and evaluation data for tier classification
     */

    console.log(`[JobCandidatesHelpers.findCandidates] Called with jobId: ${jobId}`);

    if (!jobId) {
      throw new Error('jobId is required but was not provided');
    }

    // Get job basic info with thresholds
    const job = await this.jobRepository
      .createQueryBuilder('job')
      .select([
        'job.id',
        'job.jobType',
        'job.department',
        'job.companyName',
        'job.topCandidateThreshold',
        'job.secondTierCandidateThreshold',
        'job.status',
      ])
      .where('job.id = :jobId', { jobId })
      .getOne();

    if (!job) {
      throw new NotFoundException(`Job with ID ${jobId} not found`);
    }

    // Get all candidates for this job with their evaluations (no pagination - frontend handles it)
    // Note: Only essential fields for candidate list view - excludes heavy fields like extractionMetadata, interviews, etc.
    // Include activityHistory and emailCorrespondence for getVideoIntroEmailSent method
    // IMPORTANT: We check both jobId (for direct association) and appliedJobs array (for candidates who applied to multiple jobs)
    const candidates = await this.candidateRepository
      .createQueryBuilder('candidate')
      .leftJoinAndSelect('candidate.evaluations', 'evaluations', 'evaluations.jobId = :evalJobId')
      .select([
        'candidate.id',
        'candidate.fullName',
        'candidate.jobTitle',
        'candidate.location',
        'candidate.email',
        'candidate.phone',
        'candidate.currentCompany',
        'candidate.yearsOfExperience',
        'candidate.status',
        'candidate.tier',
        'candidate.contacted',
        'candidate.isShortlisted',
        'candidate.source',
        'candidate.linkedinUrl',
        'candidate.githubUrl',
        'candidate.profileUrl',
        'candidate.experience',
        'candidate.summary',
        'candidate.contactMethod',
        'candidate.preferredLocation',
        'candidate.isRemoteOnly',
        'candidate.createdAt',
        'candidate.updatedAt',
        'candidate.activityHistory',
        'candidate.emailCorrespondence',
        'candidate.appliedJobs',
        'evaluations.id',
        'evaluations.jobId',
        'evaluations.matchScore',
        'evaluations.evaluation',
      ])
      .where(getCandidateJobWhereClause(), { jobId })
      .orderBy('evaluations.matchScore', 'DESC')
      .setParameter('evalJobId', jobId)
      .getMany();

    if (candidates.length === 0) {
      // Return structure with job info and empty candidates array
      return {
        job: {
          id: job.id,
          jobType: job.jobType,
          department: job.department,
          companyName: job.companyName,
          topCandidateThreshold: job.topCandidateThreshold || 0,
          secondTierCandidateThreshold: job.secondTierCandidateThreshold || 0,
          status: job.status,
        },
        candidates: {
          topTier: [],
          secondTier: [],
          others: [],
          shortlisted: [],
        },
        stats: {
          totalCandidates: 0,
          topTierCount: 0,
          secondTierCount: 0,
          othersCount: 0,
          shortlistedCount: 0,
        },
      };
    }

    // Get all comparisons for this job to check which candidates have been compared
    let comparisons: any[] = [];
    let comparedCandidateIds = new Set<string>();

    try {
      comparisons = await this.comparisonRepository
        .createQueryBuilder('comparison')
        .select(['comparison.id', 'comparison.candidateIds', 'comparison.status'])
        .where('comparison.jobId = :jobId', { jobId })
        .andWhere('comparison.status = :status', { status: 'completed' })
        .getMany();

      // Create a Set of candidate IDs that have been compared
      comparisons.forEach((comparison) => {
        comparison.candidateIds.forEach((candidateId: string) => {
          comparedCandidateIds.add(candidateId);
        });
      });

      // Debug logging
      console.log(
        `[JobCandidatesHelpers] Found ${comparisons.length} comparisons for job ${jobId}`,
      );
      console.log(
        `[JobCandidatesHelpers] Compared candidate IDs:`,
        Array.from(comparedCandidateIds),
      );
    } catch (error) {
      console.error(`[JobCandidatesHelpers] Error fetching comparisons:`, error);
      // Continue without comparison data if there's an error
    }

    // Calculate tier thresholds using utility function
    const { topThreshold, secondThreshold } = normalizeJobThresholds(job);

    // Group candidates by tier based on match scores
    const groupedCandidates = {
      topTier: [] as Candidate[],
      secondTier: [] as Candidate[],
      others: [] as Candidate[],
      unranked: [] as Candidate[],
      shortlisted: [] as Candidate[],
    };

    // Add jobStats and rank to each candidate for frontend compatibility
    const candidatesWithJobStats = candidates.map((candidate, index) => {
      // Get match score from either the job-specific evaluation or the legacy evaluation field
      let matchScore = 0;
      let evaluationData = null;

      // First try to get from job-specific evaluations
      if (candidate.evaluations && candidate.evaluations.length > 0) {
        const jobEvaluation = candidate.evaluations.find(
          (evaluation) => evaluation.jobId === jobId,
        );
        if (jobEvaluation) {
          matchScore = jobEvaluation.matchScore || 0;
          evaluationData = {
            matchScore: jobEvaluation.matchScore,
            lastEvaluatedAt: jobEvaluation.updatedAt,
            criterionMatchedOn: jobEvaluation.evaluation?.criterionMatchedOn || [],
            rank: matchScore >= secondThreshold ? index + 1 : null, // Only rank if meets minimum threshold
            yourReasoningForScoring: jobEvaluation.evaluation?.yourReasoningForScoring || '',
            detailedScoreAnalysis: jobEvaluation.evaluation?.detailedScoreAnalysis || {
              areasOfStrength: [],
              areasForImprovement: [],
              overallMatchPercentage: matchScore,
              specificCriteriaMatched: {
                skillsMatch: 0,
                experienceRelevance: 0,
                locationAndAvailability: 0,
              },
              detailedReasoning: {
                skillsMatch: 0,
                experienceRelevance: {
                  industryExpertise: 0,
                  yearsOfRelevantExperience: 0,
                },
                locationAndAvailability: 0,
              },
              missingCriticalRequirements: [],
            },
          };
        }
      }

      // DO NOT fallback to legacy evaluation field - this contains data from other jobs
      // If no job-specific evaluation exists, the candidate should be unranked with 0% score

      // Determine if candidate has been evaluated for this job
      const hasEvaluation = !!evaluationData;

      // Ensure we have evaluation data, but mark unranked candidates appropriately
      if (!evaluationData) {
        evaluationData = {
          matchScore: 0,
          lastEvaluatedAt: null,
          criterionMatchedOn: [],
          rank: null, // No rank for unevaluated candidates
          yourReasoningForScoring: '',
          detailedScoreAnalysis: {
            areasOfStrength: [],
            areasForImprovement: [],
            overallMatchPercentage: 0,
            specificCriteriaMatched: {
              skillsMatch: 0,
              experienceRelevance: 0,
              locationAndAvailability: 0,
            },
            detailedReasoning: {
              skillsMatch: 0,
              experienceRelevance: {
                industryExpertise: 0,
                yearsOfRelevantExperience: 0,
                totalYearsOfWorkExperience: 0,
              },
              locationAndAvailability: 0,
            },
            missingCriticalRequirements: [],
          },
        };
      }

      // Determine tier using utility function
      const tier = determineCandidateTier(matchScore, topThreshold, secondThreshold, hasEvaluation);

      // Add candidate to appropriate group
      switch (tier) {
        case 'TOP':
          groupedCandidates.topTier.push(candidate);
          break;
        case 'SECOND':
          groupedCandidates.secondTier.push(candidate);
          break;
        case 'OTHER':
          groupedCandidates.others.push(candidate);
          break;
        case 'UNRANKED':
          groupedCandidates.unranked.push(candidate);
          break;
      }

      // Check if candidate is shortlisted
      if (candidate.status === 'SHORTLISTED') {
        groupedCandidates.shortlisted.push(candidate);
      }

      const hasBeenCompared = comparedCandidateIds.has(candidate.id);

      const candidateWithStats = {
        ...candidate,
        tier,
        rank: evaluationData.rank, // Use the rank from evaluation data (null for unranked)
        evaluation: evaluationData,
        jobStats: {
          topCandidateThreshold: topThreshold,
          secondTierCandidateThreshold: secondThreshold,
        },
        // Include computed field for video intro email status
        videoIntroEmailSent: (candidate as any).getVideoIntroEmailSent?.(jobId) || false,
        // Include whether this candidate has been compared
        hasBeenCompared: hasBeenCompared,
      };

      // Ensure hasBeenCompared is included in the serialized object
      Object.defineProperty(candidateWithStats, 'hasBeenCompared', {
        value: hasBeenCompared,
        enumerable: true,
        configurable: true,
        writable: true,
      });

      return candidateWithStats;
    });

    // Calculate stats
    const stats = {
      totalCandidates: candidates.length,
      topTierCount: groupedCandidates.topTier.length,
      secondTierCount: groupedCandidates.secondTier.length,
      othersCount: groupedCandidates.others.length,
      unrankedCount: groupedCandidates.unranked.length,
      shortlistedCount: groupedCandidates.shortlisted.length,
    };

    // Return optimized structure for frontend compatibility
    return {
      job: {
        id: job.id,
        jobType: job.jobType,
        department: job.department,
        companyName: job.companyName,
        topCandidateThreshold: topThreshold,
        secondTierCandidateThreshold: secondThreshold,
        status: job.status,
      },
      candidates: {
        topTier: groupedCandidates.topTier.map((candidate) =>
          candidatesWithJobStats.find((c) => c.id === candidate.id),
        ),
        secondTier: groupedCandidates.secondTier.map((candidate) =>
          candidatesWithJobStats.find((c) => c.id === candidate.id),
        ),
        others: groupedCandidates.others.map((candidate) =>
          candidatesWithJobStats.find((c) => c.id === candidate.id),
        ),
        unranked: groupedCandidates.unranked.map((candidate) =>
          candidatesWithJobStats.find((c) => c.id === candidate.id),
        ),
        shortlisted: groupedCandidates.shortlisted.map((candidate) =>
          candidatesWithJobStats.find((c) => c.id === candidate.id),
        ),
      },
      stats,
    };
  }

  async updateCandidateRanks(
    jobId: string,
    candidateRanks: Array<{ id: string; rank: number | undefined; meetsThreshold?: boolean }>,
  ) {
    const job = await this.jobRepository.findOne({
      where: { id: jobId },
      relations: ['candidates'],
    });

    if (!job || !job.candidates) {
      throw new NotFoundException(`Job with ID ${jobId} not found or has no candidates`);
    }

    for (const rankUpdate of candidateRanks) {
      const candidate = job.candidates.filter((c: Candidate) => c.id === rankUpdate.id)[0];
      if (candidate && candidate.evaluation) {
        // If candidate doesn't meet the threshold, remove rank entirely
        if (rankUpdate.meetsThreshold === false) {
          candidate.evaluation.rank = undefined; // Remove rank for candidates below threshold
        } else {
          candidate.evaluation.rank = rankUpdate.rank;
        }
        await this.candidateRepository.save(candidate);

        // Log appropriate message based on threshold status and rank
        let logMessage: string;
        if (rankUpdate.meetsThreshold === false) {
          logMessage = 'Candidate does not meet threshold requirements - removed from rankings';
        } else {
          logMessage =
            rankUpdate.rank !== undefined
              ? `Candidate rank manually updated to ${rankUpdate.rank}`
              : 'Candidate rank removed';
        }

        await this.logCandidateActivity(candidate.id, ActivityType.EVALUATION_UPDATED, logMessage, {
          jobId,
          jobTitle: job.jobType,
          oldRank: candidate.evaluation.rank,
          newRank: rankUpdate.rank,
          belowThreshold: rankUpdate.rank === undefined,
          updatedAt: new Date(),
        });
      }
    }

    return job;
  }

  async getJobsForJobSeeker(userId: string, all?: boolean, relations?: string[]): Promise<any> {
    const jobSeeker = await this.jobSeekerService.getByUserId(userId);
    if (!jobSeeker) {
      throw new NotFoundException('Job seeker not found');
    }

    // First, let's get all the distinct job types and positions for the filters
    const jobTypesQuery = this.jobRepository
      .createQueryBuilder('job')
      .leftJoinAndSelect('job.videoJDs', 'videoJDs')
      .select('DISTINCT job.jobType', 'jobType')
      .where('job.jobType IS NOT NULL')
      .andWhere('job.isPublished = :isPublished', { isPublished: true })
      .orderBy('job.jobType', 'ASC');

    const departmentsQuery = this.jobRepository
      .createQueryBuilder('job')
      .select('DISTINCT job.department', 'department')
      .where('job.department IS NOT NULL')
      .andWhere('job.isPublished = :isPublished', { isPublished: true })
      .orderBy('job.department', 'ASC');

    const locationsQuery = this.jobRepository
      .createQueryBuilder('job')
      .select('job.location', 'location')
      .where('job.location IS NOT NULL')
      .andWhere('job.isPublished = :isPublished', { isPublished: true });

    const [jobTypesResult, departmentsResult, locationsRaw] = await Promise.all([
      jobTypesQuery.getRawMany(),
      departmentsQuery.getRawMany(),
      locationsQuery.getRawMany(),
    ]);

    // Process job categories
    const jobTypes = jobTypesResult.map((result) => result.jobType);
    const departments = departmentsResult.map((result) => result.department);

    // Process locations (may be stored as array in each job)
    const allLocations = new Set<string>();
    locationsRaw.forEach((job) => {
      if (Array.isArray(job.location)) {
        job.location.forEach((loc: string) => allLocations.add(loc));
      } else if (job.location) {
        allLocations.add(job.location);
      }
    });
    const locations = Array.from(allLocations).sort();

    // Fetch the jobs
    // Use a more specific selection to avoid column issues
    const queryBuilder = this.jobRepository
      .createQueryBuilder('job')
      .leftJoin('job.applications', 'applications', 'applications.jobSeekerId = :jobSeekerId', {
        jobSeekerId: jobSeeker.id,
      })
      // Select only the application ID to check if it exists, avoiding column issues
      .addSelect('applications.id', 'applicationId')
      .leftJoinAndSelect('job.videoJDs', 'videoJDs', 'videoJDs.status = :videoStatus', {
        videoStatus: 'COMPLETED',
      })
      .where('job.jobType IS NOT NULL')
      .andWhere('job.department IS NOT NULL')
      .andWhere('job.companyName IS NOT NULL')
      .andWhere('job.isPublished = :isPublished', { isPublished: true });

    if (!all && jobSeeker.preferences?.jobTypes?.length) {
      queryBuilder.andWhere('job.jobType IN (:...jobTypes)', {
        jobTypes: jobSeeker.preferences.jobTypes,
      });
    }

    // Add any additional relations if provided
    if (relations?.length) {
      relations.forEach((relation) => {
        queryBuilder.leftJoinAndSelect(`job.${relation}`, relation);
      });
    }

    queryBuilder.orderBy('job.createdAt', 'DESC');

    const jobs = await queryBuilder.getMany();

    // Get applications separately to avoid column issues
    const applications = await this.jobApplicationRepository.find({
      where: { jobSeekerId: jobSeeker.id },
      select: ['id', 'jobId', 'jobSeekerId'],
    });

    // Create a set of job IDs that the user has applied to
    const appliedJobIds = new Set(applications.map((app) => app.jobId));

    // Process jobs to include match percentage from metrics if available
    const processedJobs = jobs.map((job) => {
      const jobWithMetrics = {
        ...job,
        alreadyApplied: appliedJobIds.has(job.id),
        applications: undefined,
      };

      // Add match percentage and details from metrics if available
      if (job.metrics?.matchScores && job.metrics.matchScores[jobSeeker.id]) {
        (jobWithMetrics as any).matchPercentage = job.metrics.matchScores[jobSeeker.id];
      }

      // Add match details if available
      if (job.metrics?.matchDetails && job.metrics.matchDetails[jobSeeker.id]) {
        (jobWithMetrics as any).matchDetails = job.metrics.matchDetails[jobSeeker.id];
      }

      // Sort videoJDs to show the most recent completed one first
      if (job.videoJDs && job.videoJDs.length > 0) {
        job.videoJDs.sort(
          (a, b) =>
            new Date(b.updatedAt as unknown as string).getTime() -
            new Date(a.updatedAt as unknown as string).getTime(),
        );
        // Include videoJD in the response
        (jobWithMetrics as any).videoJD = job.videoJDs[0];
      }

      return jobWithMetrics;
    });

    return {
      data: processedJobs,
      metadata: {
        total: jobs.length,
        filtered: jobs.length,
        preferences: {
          jobTypes,
          departments,
          locations,
          remotePreference: jobSeeker.preferences?.remotePreference || '',
        },
      },
    };
  }

  async getJobsForGraduates(clientId: string, relations?: string[]): Promise<JobsResponse> {
    const jobSeeker = await this.graduatesService.findByClientId(clientId);
    if (!jobSeeker) {
      throw new NotFoundException('graduate not found');
    }

    const queryBuilder = this.jobRepository
      .createQueryBuilder('job')
      .leftJoin('job.applications', 'applications', 'applications.jobSeekerId = :jobSeekerId', {
        jobSeekerId: jobSeeker.id,
      })
      // Select only the application ID to check if it exists, avoiding column issues
      .addSelect('applications.id', 'applicationId')
      .where('job.jobType IS NOT NULL')
      .andWhere('job.department IS NOT NULL')
      .andWhere('job.companyName IS NOT NULL')
      .andWhere('job.isPublished = :isPublished', { isPublished: true });

    // Add any additional relations if provided
    if (relations?.length) {
      relations.forEach((relation) => {
        queryBuilder.leftJoinAndSelect(`job.${relation}`, relation);
      });
    }

    queryBuilder.orderBy('job.createdAt', 'DESC');

    const jobs = await queryBuilder.getMany();

    // Get applications separately to avoid column issues
    const applications = await this.jobApplicationRepository.find({
      where: { jobSeekerId: jobSeeker.id },
      select: ['id', 'jobId', 'jobSeekerId'],
    });

    // Create a set of job IDs that the user has applied to
    const appliedJobIds = new Set(applications.map((app) => app.jobId));

    // Create a new array of jobs with the alreadyApplied flag and match percentage
    const formattedJobs = jobs.map((job) => {
      // Create a new job object with the same prototype
      const newJob = Object.create(Object.getPrototypeOf(job));
      // Copy all properties
      (Object as any).assign(newJob, job);
      // Add alreadyApplied flag based on the set of applied job IDs
      (newJob as any).alreadyApplied = appliedJobIds.has(job.id);

      // Add match percentage and details from metrics if available
      if (job.metrics?.matchScores && job.metrics.matchScores[jobSeeker.id]) {
        (newJob as any).matchPercentage = job.metrics.matchScores[jobSeeker.id];
      }

      // Add match details if available
      if (job.metrics?.matchDetails && job.metrics.matchDetails[jobSeeker.id]) {
        (newJob as any).matchDetails = job.metrics.matchDetails[jobSeeker.id];
      }

      return newJob;
    });

    return {
      data: formattedJobs,
      metadata: {
        total: formattedJobs.length,
        filtered: formattedJobs.length,
      },
      pagination: {
        currentPage: 1,
        totalPages: 1,
        totalItems: formattedJobs.length,
        itemsPerPage: formattedJobs.length,
      },
    };
  }

  async getJobSeekerApplications(clientId: string) {
    // First, find the job seeker to get their ID
    let jobSeeker = await this.jobSeekerRepository.findOneBy({ clientId });
    if (!jobSeeker) {
      // Fallback: try to find by userId in case clientId is not set properly
      jobSeeker = await this.jobSeekerRepository.findOneBy({ userId: clientId });
    }
    if (!jobSeeker) {
      return {
        active: [],
        withdrawn: [],
        total: 0,
        activeCount: 0,
        withdrawnCount: 0,
      };
    }

    // Get all applications including soft-deleted ones
    // Use createQueryBuilder to avoid column name issues and be explicit about columns
    const allApplications = await this.jobApplicationRepository
      .createQueryBuilder('applications')
      .select([
        'applications.id',
        'applications.jobId',
        'applications.jobSeekerId',
        'applications.clientId',
        'applications.status',
        'applications.createdAt',
        'applications.updatedAt',
        'applications.deletedAt',
        'applications.coverLetter',
        'applications.withdrawalReason',
        // Don't select applicationReason as it might not exist in the database yet
      ])
      .leftJoinAndSelect('applications.job', 'job')
      .addSelect(['job.id', 'job.jobType', 'job.companyName', 'job.department', 'job.location'])
      .where('applications.jobSeekerId = :jobSeekerId', { jobSeekerId: jobSeeker.id })
      .withDeleted() // Include soft-deleted records to get withdrawn applications
      .getMany();

    // Get corresponding candidates to access statusHistory
    const candidates = await this.candidateRepository
      .createQueryBuilder('candidate')
      .select([
        'candidate.id',
        'candidate.jobId',
        'candidate.jobSeekerId',
        'candidate.statusHistory',
      ])
      .where('candidate.jobSeekerId = :jobSeekerId', { jobSeekerId: jobSeeker.id })
      .getMany();

    // Create a map of jobId to latest status from statusHistory
    const jobStatusMap = new Map<string, string>();
    candidates.forEach((candidate) => {
      if (candidate.jobId && candidate.statusHistory && candidate.statusHistory.length > 0) {
        // Get the latest status from statusHistory (last entry in the array)
        const latestStatus = candidate.statusHistory[candidate.statusHistory.length - 1];
        jobStatusMap.set(candidate.jobId, latestStatus.newStatus);
      }
    });

    // Update applications with status from statusHistory or fallback to direct status
    const applicationsWithUpdatedStatus = allApplications.map((app) => {
      const statusFromHistory = jobStatusMap.get(app.jobId);
      return {
        ...app,
        status: statusFromHistory || app.status, // Use statusHistory if available, otherwise fallback
      };
    });

    // Separate active and withdrawn applications based on updated status
    // Also consider applications with deletedAt as withdrawn regardless of status
    const activeApplications = applicationsWithUpdatedStatus.filter(
      (app) => app.status !== ApplicationStatus.WITHDRAWN && !app.deletedAt,
    );

    const withdrawnApplications = applicationsWithUpdatedStatus.filter(
      (app) => app.status === ApplicationStatus.WITHDRAWN || app.deletedAt,
    );

    return {
      active: activeApplications,
      withdrawn: withdrawnApplications,
      total: applicationsWithUpdatedStatus.length,
      activeCount: activeApplications.length,
      withdrawnCount: withdrawnApplications.length,
    };
  }

  async getJobApplicationStatusForJobSeeker(jobId: string, clientId: string) {
    return getJobApplicationStatusForCandidate(
      jobId,
      clientId,
      this.jobApplicationRepository,
      this.candidateRepository,
    );
  }

  async addCandidatesToJob(jobId: string, candidateIds: string[], userId: string): Promise<Job> {
    // Find the job
    const job = await this.jobRepository.findOne({
      where: { id: jobId },
      relations: ['candidates'],
    });

    if (!job) {
      throw new NotFoundException(`Job with ID ${jobId} not found`);
    }

    // Initialize candidates array if it doesn't exist
    if (!job.candidates) {
      job.candidates = [];
    }

    // Get existing candidate IDs to avoid duplicates
    const existingCandidateIds = job.candidates.map((candidate) => candidate.id);

    // Filter out candidates that are already in the job
    const newCandidateIds = candidateIds.filter((id) => !existingCandidateIds.includes(id));

    if (newCandidateIds.length === 0) {
      return job; // No new candidates to add
    }

    // Find the candidates to add
    const candidatesToAdd = await this.candidateRepository.findBy({
      id: In(newCandidateIds),
    });

    if (candidatesToAdd.length === 0) {
      throw new NotFoundException('No valid candidates found to add');
    }

    // Update each candidate with the job ID
    for (const candidate of candidatesToAdd) {
      candidate.jobId = jobId;

      // Add activity history entry for each candidate
      await this.logCandidateActivity(
        candidate.id,
        ActivityType.ADDED_TO_JOB,
        `Added to job: ${job.jobType}`,
        {
          jobId,
          jobTitle: job.jobType,
          addedAt: new Date(),
          addedBy: userId,
        },
      );
    }

    // Save the updated candidates
    await this.candidateRepository.save(candidatesToAdd);

    // Add candidates to the job's candidates array
    job.candidates.push(...candidatesToAdd);

    // Save the updated job
    await this.jobRepository.save(job);

    // Return the updated job with candidates
    return this.jobCrudUtils.findOne(jobId, ['candidates']);
  }

  static processJobCandidatesStatic(job: Job): Job {
    return JobCrudUtils.processJobCandidatesStatic(job);
  }

  async processJobCandidates(job: Job): Promise<Job> {
    return JobCandidatesHelpers.processJobCandidatesStatic(job);
  }

  async updateJobApplicationStatus(
    jobId: string,
    jobSeekerId: string,
    candidateStatus: CandidateStatus,
  ): Promise<void> {
    if (!jobSeekerId || !jobId) {
      return; // Skip if we don't have both IDs
    }

    try {
      // Find the job application
      const application = await this.jobApplicationRepository.findOne({
        where: { jobId, jobSeekerId },
      });

      if (!application) {
        return;
      }

      // Map candidate status to application status
      let applicationStatus: ApplicationStatus;

      switch (candidateStatus) {
        case CandidateStatus.HIRED:
          applicationStatus = ApplicationStatus.HIRED;
          break;
        case CandidateStatus.REJECTED:
          applicationStatus = ApplicationStatus.REJECTED;
          break;
        case CandidateStatus.WITHDRAWN:
          applicationStatus = ApplicationStatus.WITHDRAWN;
          break;
        case CandidateStatus.SHORTLISTED:
          applicationStatus = ApplicationStatus.SHORTLISTED;
          break;
        case CandidateStatus.INTERVIEWING:
        case CandidateStatus.OFFER_PENDING_APPROVAL:
        case CandidateStatus.OFFER_APPROVED:
        case CandidateStatus.OFFER_EXTENDED:
        case CandidateStatus.OFFER_ACCEPTED:
        case CandidateStatus.HIRE_PENDING_APPROVAL:
        case CandidateStatus.HIRE_APPROVED:
          applicationStatus = ApplicationStatus.REVIEWING;
          break;
        default:
          applicationStatus = ApplicationStatus.APPLIED;
      }

      // Update the application status
      await this.jobApplicationRepository.update(application.id, {
        status: applicationStatus,
      });
    } catch (error) {
      console.error('Error updating job application status:', error);
    }
  }
}
