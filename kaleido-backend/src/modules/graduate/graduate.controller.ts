import { memoryStorage } from 'multer';
import * as QRCode from 'qrcode';

import { Auth0Guard } from '@/auth/auth.guard';
import { Public } from '@/auth/public.decorator';
import { GetUser, User } from '@/shared/decorators/get-user.decorator';
import { FILE_UPLOAD } from '@/shared/types/constants';
import {
  BadRequestException,
  Body,
  Controller,
  Delete,
  FileTypeValidator,
  Get,
  MaxFileSizeValidator,
  NotFoundException,
  Param,
  ParseFilePipe,
  Patch,
  Post,
  Query,
  UploadedFile,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
// import { FileInterceptor } from '@nestjs/platform-express';
import { FastifyFileInterceptor } from '@/shared/interceptors/fastify-file.interceptor';
import {
  ApiBearerAuth,
  ApiBody,
  ApiConsumes,
  ApiOperation,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { Roles } from '@shared/decorators/roles.decorator';
import { UserRole } from '@shared/types';

import { CreateGraduateDto } from './dto/create-graduate.dto';
import { UpdateGraduateDto } from './dto/update-graduate.dto';
import { Graduate } from './entities/graduate.entity';
import { GraduateService } from './graduate.service';

@ApiTags('graduates')
@Controller('graduates')
@UseGuards(Auth0Guard)
@ApiBearerAuth()
export class GraduateController {
  constructor(private readonly graduateService: GraduateService) {}

  @Post()
  @Roles(UserRole.GRADUATE)
  create(@GetUser() user: User, @Body() createGraduateDto: CreateGraduateDto) {
    return this.graduateService.create({
      ...createGraduateDto,
      clientId: user.userId,
    });
  }

  @Get()
  @Roles(UserRole.ADMIN)
  findAll() {
    return this.graduateService.findAll();
  }

  @Get('admin/all')
  // @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Get all graduates with pagination (admin only)' })
  @ApiResponse({
    status: 200,
    description: 'Returns paginated list of all graduates',
  })
  async getAllGraduates(@Query('page') page = 1, @Query('limit') limit = 10) {
    return this.graduateService.findAllPaginated(Number(page), Number(limit));
  }

  @Get('admin/grouped-by-status')
  // @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Get all graduates grouped by approval status (admin only)' })
  @ApiResponse({
    status: 200,
    description: 'Returns graduates grouped by approval status',
  })
  async getAllGraduatesGroupedByStatus(): Promise<{
    approved: Graduate[];
    pending: Graduate[];
  }> {
    return this.graduateService.findAllGroupedByStatus();
  }

  @Get('profile')
  @Roles(UserRole.GRADUATE, UserRole.ADMIN)
  findProfile(@GetUser() user: User) {
    return this.graduateService.findByClientId(user.userId);
  }

  @Patch('profile')
  @Roles(UserRole.GRADUATE, UserRole.ADMIN)
  async updateProfile(@GetUser() user: User, @Body() updateGraduateDto: UpdateGraduateDto) {
    const graduate = await this.graduateService.findByClientId(user.userId);
    return this.graduateService.update(graduate.id, updateGraduateDto);
  }

  @Post('validate-profile')
  @Roles(UserRole.GRADUATE, UserRole.ADMIN)
  validateProfile(@Body() user: User) {
    return this.graduateService.validateProfile(user);
  }

  @Get(':id')
  @Roles(UserRole.GRADUATE, UserRole.ADMIN)
  findOne(@Param('id') id: string) {
    return this.graduateService.findOne(id);
  }

  @Patch(':id')
  @Roles(UserRole.GRADUATE, UserRole.ADMIN)
  update(@Param('id') id: string, @Body() updateGraduateDto: UpdateGraduateDto) {
    return this.graduateService.update(id, updateGraduateDto);
  }

  @Delete(':id')
  @Roles(UserRole.GRADUATE, UserRole.ADMIN)
  remove(@Param('id') id: string) {
    return this.graduateService.remove(id);
  }

  @Post(':id/approve')
  @Roles(UserRole.ADMIN)
  approveGraduate(@Param('id') id: string) {
    return this.graduateService.approve(id);
  }

  @Post(':id/decline')
  @Roles(UserRole.ADMIN)
  declineGraduate(@Param('id') id: string, @Body() body: { reason: string }) {
    return this.graduateService.decline(id, body.reason);
  }

  @Post(':id/convert-to-job-seeker')
  @Roles(UserRole.GRADUATE)
  convertToJobSeeker(@Param('id') id: string) {
    return this.graduateService.convertToJobSeeker(id);
  }

  @Post('upload/resume')
  @UseInterceptors(new FastifyFileInterceptor('file'))
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  async uploadResume(
    @UploadedFile(
      new ParseFilePipe({
        validators: [
          new FileTypeValidator({
            fileType: '.(pdf|doc|docx|txt)$',
          }),
          new MaxFileSizeValidator({
            maxSize: FILE_UPLOAD.MAX_SIZE * 2,
          }),
        ],
        errorHttpStatusCode: 400,
        fileIsRequired: true,
      }),
    )
    file: Express.Multer.File,
    @GetUser() authUser: User,
  ) {
    if (!file) {
      throw new BadRequestException('No file uploaded');
    }

    try {
      const userId = authUser.userId || `temp_${Date.now()}`;
      const graduate = await this.graduateService.uploadResume(userId, file);

      // Return both the URL and the parsed graduate data
      return {
        resumeUrl: graduate.resumeUrl,
        ...graduate,
        message: 'Resume uploaded and processed successfully',
      };
    } catch (error: any) {
      throw new BadRequestException(error.message);
    }
  }

  @Get('public/:id')
  @Public()
  async getPublicProfile(@Param('id') id: string) {
    return this.graduateService.getPublicProfile(id);
  }

  @Get(':id/generate-qr')
  async generateQRCode(@Param('id') id: string) {
    const graduate = await this.graduateService.findOne(id);
    if (!graduate) {
      throw new NotFoundException('Graduate not found');
    }

    // Generate or update public profile URL if not exists
    const publicUrl = graduate.generatePublicProfileUrl(
      process.env.APP_URL || 'http://localhost:3000',
    );

    // Generate QR code
    const qrCodeDataUrl = await QRCode.toDataURL(publicUrl);

    // Update graduate with QR code URL - use a type assertion to bypass DTO validation
    // This is safe because we know these properties exist on the entity
    await this.graduateService.update(id, {
      qrCodeUrl: qrCodeDataUrl,
    } as UpdateGraduateDto);

    return {
      publicProfileUrl: publicUrl,
      qrCodeUrl: qrCodeDataUrl,
    };
  }

  @Post(':userId/profile-image')
  @UseInterceptors(new FastifyFileInterceptor('image'))
  async uploadProfileImage(
    @Param('userId') userId: string,
    @GetUser() user: User,
    @UploadedFile() file: Express.Multer.File,
  ) {
    if (!userId || userId.trim() === '') {
      throw new BadRequestException('Invalid or missing user ID');
    }

    if (!file) {
      throw new BadRequestException('No image file uploaded');
    }

    return this.graduateService.uploadProfileImage(user, file);
  }

  @Post('profile-image')
  @Roles(UserRole.GRADUATE)
  @UseInterceptors(new FastifyFileInterceptor('image'))
  async uploadProfileImageForCurrentUser(
    @GetUser() user: User,
    @UploadedFile() file: Express.Multer.File,
  ) {
    if (!user || !user.userId) {
      throw new BadRequestException('User authentication required');
    }

    if (!file) {
      throw new BadRequestException('No image file uploaded');
    }

    return this.graduateService.uploadProfileImage(user, file);
  }
}
