import { Column, Entity, Index, OneToMany, OneToOne } from 'typeorm';
import { BaseEntity } from '@shared/entities/base.entity';
import { JobSeeker } from '@modules/job-seeker/entities/job-seeker.entity';
import { Company } from '@modules/company/entities/company.entity';
import { Referral } from './referral.entity';

export interface ReferralPartnerSettings {
  defaultBountyPercentage?: number;
  customBountyRules?: any[];
  paymentPreferences?: {
    method: string;
    details: any;
  };
}

export interface DashboardMetrics {
  totalReferrals: number;
  successfulPlacements: number;
  conversionRate: number;
  averageBounty: number;
}

@Entity('referral_partners')
@Index(['referralCode'], { unique: true })
@Index(['clientId'], { unique: true })
export class ReferralPartner extends BaseEntity {
  @Column({ unique: true })
  referralCode!: string; // Unique code like 'REF-ABC123'

  @Column({ unique: true })
  clientId!: string; // Links to UserRoleEntity

  @Column({ nullable: true })
  companyId?: string; // Links to Company if partner is company-specific

  @Column()
  partnerName!: string;

  @Column()
  contactEmail!: string;

  @Column({ nullable: true })
  contactPhone?: string;

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0 })
  totalEarnings!: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0 })
  pendingEarnings!: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0 })
  paidEarnings!: number;

  @Column({ default: true })
  isActive!: boolean;

  @Column({ nullable: true })
  stripeAccountId?: string;

  @Column({
    type: 'enum',
    enum: ['pending', 'active', 'restricted', 'disabled'],
    nullable: true,
  })
  stripeAccountStatus?: 'pending' | 'active' | 'restricted' | 'disabled';

  @Column({ type: 'jsonb', nullable: true })
  settings?: ReferralPartnerSettings;

  @Column({ type: 'jsonb', nullable: true })
  dashboardMetrics?: DashboardMetrics;

  // Connections to existing accounts
  @Column({ nullable: true })
  jobSeekerId?: string;

  @OneToOne(() => JobSeeker, (jobSeeker) => jobSeeker.referralPartner)
  jobSeeker?: JobSeeker;

  @OneToOne(() => Company, (company) => company.asReferralPartner)
  company?: Company;

  // Relations
  @OneToMany(() => Referral, (referral) => referral.referralPartner)
  referrals!: Referral[];
}
