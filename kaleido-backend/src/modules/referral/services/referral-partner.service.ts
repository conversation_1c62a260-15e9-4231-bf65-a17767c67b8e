import {
  Injectable,
  NotFoundException,
  ConflictException,
  Inject,
  forwardRef,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ReferralPartner } from '../entities/referral-partner.entity';
import { CreateReferralPartnerDto, UpdateReferralPartnerDto } from '../dto';
import { randomBytes } from 'crypto';
import { AuthService } from '../../../auth/auth.service';
import { RolesService } from '../../roles/roles.service';
import { UserRole } from '../../../common/enums/role.enum';

@Injectable()
export class ReferralPartnerService {
  constructor(
    @InjectRepository(ReferralPartner)
    private referralPartnerRepository: Repository<ReferralPartner>,
    @Inject(forwardRef(() => AuthService))
    private authService: AuthService,
    @Inject(forwardRef(() => RolesService))
    private rolesService: RolesService,
  ) {}

  async create(createDto: CreateReferralPartnerDto, clientId: string): Promise<ReferralPartner> {
    // Check if partner already exists for this client
    const existingPartner = await this.referralPartnerRepository.findOne({
      where: { clientId },
    });

    if (existingPartner) {
      throw new ConflictException('Referral partner already exists for this client');
    }

    // Generate unique referral code
    const referralCode = await this.generateUniqueReferralCode();

    const partner = this.referralPartnerRepository.create({
      ...createDto,
      clientId,
      referralCode,
      totalEarnings: 0,
      pendingEarnings: 0,
      paidEarnings: 0,
      isActive: true,
      dashboardMetrics: {
        totalReferrals: 0,
        successfulPlacements: 0,
        conversionRate: 0,
        averageBounty: 0,
      },
    });

    // Save the partner first
    const savedPartner = await this.referralPartnerRepository.save(partner);

    // Now add REFERRAL_PARTNER as an additional role
    try {
      // Add the role as an additional role, not primary
      await this.rolesService.addAdditionalRole(clientId, UserRole.REFERRAL_PARTNER);

      // Note: We're not updating Auth0 role as it should remain the primary role
      // The backend will check additionalRoles for authorization
    } catch (error) {
      // Log the error but don't fail the partner creation
      console.error('Error adding referral partner role:', error);
      console.error('Error details:', {
        clientId,
        role: UserRole.REFERRAL_PARTNER,
        errorMessage: error.message,
        errorStack: error.stack,
      });
      // You might want to add a flag or notification that role update failed
    }

    return savedPartner;
  }

  async findAll(companyId?: string): Promise<ReferralPartner[]> {
    const query = this.referralPartnerRepository.createQueryBuilder('partner');

    if (companyId) {
      query.where('partner.companyId = :companyId', { companyId });
    }

    return query.getMany();
  }

  async findOne(id: string): Promise<ReferralPartner> {
    const partner = await this.referralPartnerRepository.findOne({
      where: { id },
      relations: ['referrals'],
    });

    if (!partner) {
      throw new NotFoundException(`Referral partner with ID ${id} not found`);
    }

    return partner;
  }

  async findByClientId(clientId: string): Promise<ReferralPartner | null> {
    return this.referralPartnerRepository.findOne({
      where: { clientId },
    });
  }

  async findByReferralCode(referralCode: string): Promise<ReferralPartner | null> {
    return this.referralPartnerRepository.findOne({
      where: { referralCode },
    });
  }

  async update(id: string, updateDto: UpdateReferralPartnerDto): Promise<ReferralPartner> {
    const partner = await this.findOne(id);

    Object.assign(partner, updateDto);

    return this.referralPartnerRepository.save(partner);
  }

  async updateMetrics(id: string): Promise<void> {
    const partner = await this.referralPartnerRepository.findOne({
      where: { id },
      relations: ['referrals'],
    });

    if (!partner) {
      throw new NotFoundException(`Referral partner with ID ${id} not found`);
    }

    const referrals = partner.referrals || [];
    const successfulPlacements = referrals.filter(
      (r) =>
        r.status === 'CANDIDATE_HIRED' ||
        r.status === 'BOUNTY_APPROVED' ||
        r.status === 'BOUNTY_PAID',
    ).length;

    const totalBounties = referrals
      .filter((r) => r.bountyAmount)
      .reduce((sum, r) => sum + Number(r.bountyAmount), 0);

    partner.dashboardMetrics = {
      totalReferrals: referrals.length,
      successfulPlacements,
      conversionRate: referrals.length > 0 ? (successfulPlacements / referrals.length) * 100 : 0,
      averageBounty: successfulPlacements > 0 ? totalBounties / successfulPlacements : 0,
    };

    await this.referralPartnerRepository.save(partner);
  }

  async updateEarnings(id: string, amount: number, type: 'pending' | 'paid'): Promise<void> {
    const partner = await this.findOne(id);

    if (type === 'pending') {
      partner.pendingEarnings = Number(partner.pendingEarnings) + amount;
    } else if (type === 'paid') {
      partner.pendingEarnings = Number(partner.pendingEarnings) - amount;
      partner.paidEarnings = Number(partner.paidEarnings) + amount;
    }

    partner.totalEarnings = Number(partner.paidEarnings) + Number(partner.pendingEarnings);

    await this.referralPartnerRepository.save(partner);
  }

  async deactivate(id: string): Promise<void> {
    await this.referralPartnerRepository.update(id, { isActive: false });
  }

  async activate(id: string): Promise<void> {
    await this.referralPartnerRepository.update(id, { isActive: true });
  }

  private async generateUniqueReferralCode(): Promise<string> {
    let code: string;
    let exists: boolean;

    do {
      // Generate a random 8-character alphanumeric string
      const randomString = randomBytes(6)
        .toString('base64')
        .replace(/[+/=]/g, '') // Remove non-alphanumeric characters
        .substring(0, 8)
        .toUpperCase();

      code = `REF-${randomString}`;
      exists = await this.referralPartnerRepository.exist({
        where: { referralCode: code },
      });
    } while (exists);

    return code;
  }
}
