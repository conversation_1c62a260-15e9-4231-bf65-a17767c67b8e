import { Injectable, ConflictException, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ReferralPartner } from '../entities/referral-partner.entity';
import { JobSeeker } from '@modules/job-seeker/entities/job-seeker.entity';
import { Company } from '@modules/company/entities/company.entity';
import { UserRoleEntity } from '@modules/roles/entities/user-role.entity';
import { UserRole } from '@/common/enums/role.enum';
import * as crypto from 'crypto';

@Injectable()
export class ReferralActivationService {
  constructor(
    @InjectRepository(ReferralPartner)
    private referralPartnerRepository: Repository<ReferralPartner>,
    @InjectRepository(JobSeeker)
    private jobSeekerRepository: Repository<JobSeeker>,
    @InjectRepository(Company)
    private companyRepository: Repository<Company>,
    @InjectRepository(UserRoleEntity)
    private userRoleRepository: Repository<UserRoleEntity>,
  ) {}

  async activateForJobSeeker(jobSeekerId: string, clientId: string): Promise<ReferralPartner> {
    // Check if job seeker exists
    const jobSeeker = await this.jobSeekerRepository.findOne({
      where: { id: jobSeekerId },
    });

    if (!jobSeeker) {
      throw new NotFoundException('Job seeker not found');
    }

    // Check if already activated
    if (jobSeeker.isReferralPartner && jobSeeker.referralPartnerId) {
      const existingPartner = await this.referralPartnerRepository.findOne({
        where: { id: jobSeeker.referralPartnerId },
      });
      if (existingPartner) {
        return existingPartner;
      }
    }

    // Create referral partner
    const referralCode = this.generateReferralCode();
    const referralPartner = this.referralPartnerRepository.create({
      referralCode,
      clientId,
      jobSeekerId,
      partnerName: `${jobSeeker.firstName} ${jobSeeker.lastName}`,
      contactEmail: jobSeeker.email,
      contactPhone: jobSeeker.phone,
      isActive: true,
      totalEarnings: 0,
      pendingEarnings: 0,
      paidEarnings: 0,
      dashboardMetrics: {
        totalReferrals: 0,
        successfulPlacements: 0,
        conversionRate: 0,
        averageBounty: 0,
      },
    });

    const savedPartner = await this.referralPartnerRepository.save(referralPartner);

    // Update job seeker
    await this.jobSeekerRepository.update(jobSeekerId, {
      referralPartnerId: savedPartner.id,
      isReferralPartner: true,
    });

    // Add REFERRAL_PARTNER to additional roles
    await this.addReferralPartnerRole(clientId);

    return savedPartner;
  }

  async activateForCompany(companyId: string, clientId: string): Promise<ReferralPartner> {
    // Check if company exists
    const company = await this.companyRepository.findOne({
      where: { id: companyId },
    });

    if (!company) {
      throw new NotFoundException('Company not found');
    }

    // Check if already activated
    if (company.referralPartnerId) {
      const existingPartner = await this.referralPartnerRepository.findOne({
        where: { id: company.referralPartnerId },
      });
      if (existingPartner) {
        return existingPartner;
      }
    }

    // Create referral partner
    const referralCode = this.generateReferralCode();
    const referralPartner = this.referralPartnerRepository.create({
      referralCode,
      clientId,
      companyId,
      partnerName: company.companyName,
      contactEmail: company.contactEmail,
      contactPhone: company.phoneNumber,
      isActive: true,
      totalEarnings: 0,
      pendingEarnings: 0,
      paidEarnings: 0,
      dashboardMetrics: {
        totalReferrals: 0,
        successfulPlacements: 0,
        conversionRate: 0,
        averageBounty: 0,
      },
    });

    const savedPartner = await this.referralPartnerRepository.save(referralPartner);

    // Update company
    await this.companyRepository.update(companyId, {
      referralPartnerId: savedPartner.id,
    });

    // Add REFERRAL_PARTNER to additional roles
    await this.addReferralPartnerRole(clientId);

    return savedPartner;
  }

  async deactivateReferralPartner(
    userId: string,
    userType: 'jobseeker' | 'company',
  ): Promise<void> {
    let referralPartnerId: string | undefined;
    let clientId: string | undefined;

    if (userType === 'jobseeker') {
      const jobSeeker = await this.jobSeekerRepository.findOne({
        where: { id: userId },
      });
      if (!jobSeeker || !jobSeeker.referralPartnerId) {
        throw new NotFoundException('Referral partner not found for this job seeker');
      }
      referralPartnerId = jobSeeker.referralPartnerId;
      clientId = jobSeeker.clientId;

      // Clear referral partner from job seeker
      await this.jobSeekerRepository.update(userId, {
        referralPartnerId: undefined,
        isReferralPartner: false,
      });
    } else {
      const company = await this.companyRepository.findOne({
        where: { id: userId },
      });
      if (!company || !company.referralPartnerId) {
        throw new NotFoundException('Referral partner not found for this company');
      }
      referralPartnerId = company.referralPartnerId;
      clientId = company.clientId;

      // Clear referral partner from company
      await this.companyRepository.update(userId, {
        referralPartnerId: undefined,
      });
    }

    // Deactivate referral partner
    await this.referralPartnerRepository.update(referralPartnerId, {
      isActive: false,
    });

    // Remove REFERRAL_PARTNER from additional roles
    if (clientId) {
      await this.removeReferralPartnerRole(clientId);
    }
  }

  private generateReferralCode(): string {
    // Generate a secure random code
    const randomBytes = crypto.randomBytes(4).toString('hex').toUpperCase();
    return `REF-${randomBytes}`;
  }

  private async addReferralPartnerRole(clientId: string): Promise<void> {
    const userRole = await this.userRoleRepository.findOne({
      where: { clientId },
    });

    if (userRole) {
      const additionalRoles = userRole.additionalRoles || [];
      if (!additionalRoles.includes(UserRole.REFERRAL_PARTNER)) {
        additionalRoles.push(UserRole.REFERRAL_PARTNER);
        await this.userRoleRepository.update(userRole.id, {
          additionalRoles,
        });
      }
    } else {
      // If no user role exists, this shouldn't happen in practice
      console.error(`No user role found for clientId: ${clientId}`);
    }
  }

  private async removeReferralPartnerRole(clientId: string): Promise<void> {
    const userRole = await this.userRoleRepository.findOne({
      where: { clientId },
    });

    if (userRole && userRole.additionalRoles) {
      const additionalRoles = userRole.additionalRoles.filter(
        (role) => role !== UserRole.REFERRAL_PARTNER,
      );
      await this.userRoleRepository.update(userRole.id, {
        additionalRoles,
      });
    }
  }
}
