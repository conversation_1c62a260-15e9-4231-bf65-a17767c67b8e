import { Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ReferralPartner, Referral, BountyConfiguration } from './entities';
import {
  ReferralPartnerService,
  ReferralService,
  BountyCalculationService,
  ReferralActivationService,
} from './services';
import {
  ReferralPartnerController,
  ReferralController,
  ReferralEarningsController,
  ReferralPublicController,
  ReferralPartnerPublicController,
  ReferralActivationController,
} from './controllers';
import { AuthModule } from '../../auth/auth.module';
import { RolesModule } from '../roles/roles.module';
import { Job } from '../job/entities/job.entity';
import { Candidate } from '../candidate/entities/candidate.entity';
import { Company } from '../company/entities/company.entity';
import { JobSeeker } from '../job-seeker/entities/job-seeker.entity';
import { UserRoleEntity } from '../roles/entities/user-role.entity';
import { JobSeekerModule } from '../job-seeker/job-seeker.module';
import { CompanyModule } from '../company/company.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      ReferralPartner,
      Referral,
      BountyConfiguration,
      Job,
      Candidate,
      Company,
      JobSeeker,
      UserRoleEntity,
    ]),
    forwardRef(() => AuthModule),
    forwardRef(() => RolesModule),
    forwardRef(() => JobSeekerModule),
    forwardRef(() => CompanyModule),
  ],
  controllers: [
    ReferralPartnerController,
    ReferralPartnerPublicController,
    ReferralController,
    ReferralEarningsController,
    ReferralPublicController,
    ReferralActivationController,
  ],
  providers: [
    ReferralPartnerService,
    ReferralService,
    BountyCalculationService,
    ReferralActivationService,
  ],
  exports: [
    ReferralPartnerService,
    ReferralService,
    BountyCalculationService,
    ReferralActivationService,
  ],
})
export class ReferralModule {}
