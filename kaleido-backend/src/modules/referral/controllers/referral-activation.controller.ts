import { Controller, Post, Delete, UseGuards, NotFoundException } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { Auth0Guard } from '@/auth/auth.guard';
import { Roles } from '@/shared/decorators/roles.decorator';
import { GetUser, User } from '@/shared/decorators/get-user.decorator';
import { UserRole } from '@/common/enums/role.enum';
import { ReferralActivationService } from '../services/referral-activation.service';
import { ReferralPartner } from '../entities/referral-partner.entity';
import { JobSeekerService } from '@/modules/job-seeker/job-seeker.service';
import { CompanyService } from '@/modules/company/company.service';

@ApiTags('Referral Activation')
@Controller('referral/activation')
@UseGuards(Auth0Guard)
@ApiBearerAuth()
export class ReferralActivationController {
  constructor(
    private readonly activationService: ReferralActivationService,
    private readonly jobSeekerService: JobSeekerService,
    private readonly companyService: CompanyService,
  ) {}

  @Post('job-seeker')
  @Roles(UserRole.JOB_SEEKER, UserRole.GRADUATE)
  @ApiOperation({ summary: 'Activate referral program for job seeker' })
  @ApiResponse({
    status: 201,
    description: 'Referral program activated successfully',
    type: ReferralPartner,
  })
  @ApiResponse({
    status: 409,
    description: 'Referral program already activated',
  })
  async activateForJobSeeker(@GetUser() user: User): Promise<ReferralPartner> {
    const clientId = user.sub || user.userId;
    const jobSeeker = await this.jobSeekerService.getByClientId(clientId);
    if (!jobSeeker) {
      throw new NotFoundException('Job seeker profile not found');
    }
    return this.activationService.activateForJobSeeker(jobSeeker.id, clientId);
  }

  @Post('company')
  @Roles(UserRole.EMPLOYER)
  @ApiOperation({ summary: 'Activate referral program for company' })
  @ApiResponse({
    status: 201,
    description: 'Referral program activated successfully',
    type: ReferralPartner,
  })
  @ApiResponse({
    status: 409,
    description: 'Referral program already activated',
  })
  async activateForCompany(@GetUser() user: User): Promise<ReferralPartner> {
    const clientId = user.sub || user.userId;
    const company = await this.companyService.findByClientId(clientId);
    if (!company) {
      throw new NotFoundException('Company profile not found');
    }
    return this.activationService.activateForCompany(company.id, clientId);
  }

  @Delete('deactivate')
  @Roles(UserRole.JOB_SEEKER, UserRole.GRADUATE, UserRole.EMPLOYER)
  @ApiOperation({ summary: 'Deactivate referral program' })
  @ApiResponse({
    status: 200,
    description: 'Referral program deactivated successfully',
  })
  @ApiResponse({
    status: 404,
    description: 'Referral partner not found',
  })
  async deactivateReferralPartner(@GetUser() user: User): Promise<void> {
    const clientId = user.sub || user.userId;
    const userType = user.roles?.includes(UserRole.EMPLOYER) ? 'company' : 'jobseeker';

    if (userType === 'company') {
      const company = await this.companyService.findByClientId(clientId);
      if (!company) {
        throw new NotFoundException('Company profile not found');
      }
      return this.activationService.deactivateReferralPartner(company.id, userType);
    } else {
      const jobSeeker = await this.jobSeekerService.getByClientId(clientId);
      if (!jobSeeker) {
        throw new NotFoundException('Job seeker profile not found');
      }
      return this.activationService.deactivateReferralPartner(jobSeeker.id, userType);
    }
  }
}
