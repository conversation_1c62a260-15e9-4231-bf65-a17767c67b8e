import { createConnection } from 'typeorm';
import * as dotenv from 'dotenv';
import * as path from 'path';

// Load production environment variables
dotenv.config({ path: path.resolve(__dirname, '../../.env.production') });

async function fixComparisonDataColumn() {
  console.log('🔧 Fixing candidate_comparisons comparisonData column...\n');

  // Build connection configuration from individual env vars
  const connection = await createConnection({
    type: 'postgres',
    host: process.env.DB_HOST,
    port: parseInt(process.env.DB_PORT || '5432'),
    username: process.env.DB_USERNAME,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_NAME,
    ssl: process.env.DB_SSL === 'require' ? { rejectUnauthorized: false } : false,
    logging: true,
  });

  try {
    // Check if comparisonData column exists
    const columnInfo = await connection.query(`
      SELECT column_name, data_type, is_nullable
      FROM information_schema.columns
      WHERE table_name = 'candidate_comparisons'
      AND column_name = 'comparisonData'
      AND table_schema = 'public'
    `);

    if (columnInfo.length > 0) {
      console.log('❌ Found legacy comparisonData column that needs to be removed');
      console.log('Column info:', columnInfo[0]);

      // Check if there's any data in the table
      const rowCount = await connection.query(
        'SELECT COUNT(*) as count FROM candidate_comparisons',
      );
      console.log(`\nTable has ${rowCount[0].count} rows`);

      // Since the table is empty, we can safely drop the column
      if (rowCount[0].count === '0') {
        console.log('\n✅ Table is empty, safe to drop the column');

        // Drop the comparisonData column
        console.log('Dropping comparisonData column...');
        await connection.query(`
          ALTER TABLE "candidate_comparisons"
          DROP COLUMN "comparisonData"
        `);

        console.log('✅ Successfully dropped comparisonData column');
      } else {
        console.log('\n⚠️  Table has data. Need to migrate data before dropping column.');
        // If there was data, we'd need to migrate it first
      }
    } else {
      console.log('✅ comparisonData column not found - schema is correct');
    }

    // Verify final schema
    console.log('\n📋 Final schema check:');
    const finalSchema = await connection.query(`
      SELECT column_name, data_type, is_nullable
      FROM information_schema.columns
      WHERE table_name = 'candidate_comparisons'
      AND table_schema = 'public'
      ORDER BY ordinal_position
    `);

    console.table(
      finalSchema.map((col: any) => ({
        column: col.column_name,
        type: col.data_type,
        nullable: col.is_nullable,
      })),
    );
  } catch (error) {
    console.error('❌ Error fixing column:', error);
    throw error;
  } finally {
    await connection.close();
  }
}

// Run the fix
fixComparisonDataColumn()
  .then(() => {
    console.log('\n✅ Column fix completed successfully!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n❌ Column fix failed:', error);
    process.exit(1);
  });
