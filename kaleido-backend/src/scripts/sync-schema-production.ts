// Register TypeScript path aliases
import './register-ts-paths';

import { config } from 'dotenv';
import { DataSource } from 'typeorm';

// Load environment variables
config();

/**
 * Production-Safe Schema Synchronization Script
 *
 * This script checks for missing tables and provides SQL to create them.
 * It does NOT use TypeORM's synchronize feature to avoid dropping columns or types.
 */
async function synchronizeSchema() {
  console.log('🔄 Starting PRODUCTION-SAFE database schema check...');
  console.log('📝 This will check for missing tables and generate SQL to create them.');
  console.log('🛡️  NO automatic changes will be made to the database.');
  console.log('');

  // Get database connection info from environment variables
  const dbHost = process.env.DB_HOST || 'localhost';
  const dbPort = parseInt(process.env.DB_PORT || '5432');
  const dbName = process.env.DB_NAME || '';
  const dbUsername = process.env.DB_USERNAME || '';
  const dbPassword = process.env.DB_PASSWORD || '';

  console.log(`📡 Connecting to database: ${dbHost}:${dbPort}/${dbName}`);

  // Create a DataSource for checking schema
  const AppDataSource = new DataSource({
    type: 'postgres',
    host: dbHost,
    port: dbPort,
    username: dbUsername,
    password: dbPassword,
    database: dbName,
    entities: [`${__dirname}/../**/*.entity.{ts,js}`],
    synchronize: false,
    dropSchema: false,
    ssl:
      process.env.DB_SSL === 'require'
        ? {
            rejectUnauthorized: false,
          }
        : false,
  });

  try {
    // Initialize the data source
    console.log('🔌 Initializing database connection...');
    await AppDataSource.initialize();
    console.log('✅ Database connection established.');

    // Get the query runner
    const queryRunner = AppDataSource.createQueryRunner();
    await queryRunner.connect();

    console.log('🔍 Checking database schema...');
    console.log('');

    // Check for critical missing tables
    const criticalTables = [
      'companies',
      'company_members',
      'company_invitations',
      'candidates',
      'jobs',
      'referrals',
      'referral_partners',
      'bounty_configurations',
    ];

    const missingTables: string[] = [];

    for (const tableName of criticalTables) {
      const tableExists = await queryRunner.hasTable(tableName);
      if (!tableExists) {
        missingTables.push(tableName);
        console.log(`❌ Missing table: ${tableName}`);
      } else {
        console.log(`✅ Table exists: ${tableName}`);
      }
    }

    // Release query runner
    await queryRunner.release();

    if (missingTables.length > 0) {
      console.log('');
      console.log('⚠️  WARNING: Missing tables detected!');
      console.log('');
      console.log('🔧 To fix this issue, you should:');
      console.log('');
      console.log('1. Generate a new migration to create the missing tables:');
      console.log('   pnpm migration:generate src/migrations/AddMissingTables');
      console.log('');
      console.log('2. Review the generated migration file');
      console.log('');
      console.log('3. Run the migration:');
      console.log('   pnpm migration:run');
      console.log('');
      console.log('❗ DO NOT use synchronize in production as it may drop columns/types!');
    } else {
      console.log('');
      console.log('✅ All critical tables exist in the database.');
      console.log('');
      console.log('📝 If you still have schema issues:');
      console.log('1. Generate a migration: pnpm migration:generate src/migrations/UpdateSchema');
      console.log('2. Review and run it: pnpm migration:run');
    }

    // Close the connection
    await AppDataSource.destroy();
    console.log('');
    console.log('🔌 Database connection closed.');

    // Exit with error if tables are missing
    if (missingTables.length > 0) {
      process.exit(1);
    }
  } catch (error) {
    console.error('❌ Error during schema check:', error);
    process.exit(1);
  }
}

// Run the schema check function
synchronizeSchema();
