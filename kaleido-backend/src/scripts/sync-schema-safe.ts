// Register TypeScript path aliases
import './register-ts-paths';

import { config } from 'dotenv';
import { DataSource, Table, TableIndex, TableColumn, TableForeignKey } from 'typeorm';

// Import all entities
import { CreditPurchase } from '../entities/CreditPurchase.entity';
import { ApprovalStep } from '../modules/approval/entities/approval-step.entity';
import { Approval } from '../modules/approval/entities/approval.entity';
import { Offer } from '../modules/approval/entities/offer.entity';
import { RecruitmentAssessment } from '../modules/assessment/entities/recruitment-assessment.entity';
import { CandidateApplication } from '../modules/candidate/entities/candidate-application.entity';
import { CandidateEvaluation } from '../modules/candidate/entities/candidate-evaluation.entity';
import { CandidateProfile } from '../modules/candidate/entities/candidate-profile.entity';
import { Candidate } from '../modules/candidate/entities/candidate.entity';
import { ScoutedCandidate } from '../modules/candidate/entities/scouted-candidate.entity';
import { CareerInsight } from '../modules/career-insights/entities/career-insight.entity';
import { Company } from '../modules/company/entities/company.entity';
import { CompanyMember } from '../modules/company/entities/company-member.entity';
import { CompanyInvitation } from '../modules/company/entities/company-invitation.entity';
import { CandidateComparison } from '../modules/comparison/entities/candidate-comparison.entity';
import { Contact } from '../modules/contact/entities/contact.entity';
import { EmailHistory } from '../modules/email/entities/email-history.entity';
import { FeatureFlagEntity } from '../modules/feature-flags/entities/feature-flag.entity';
import { Feedback } from '../modules/feedback/entities/feedback.entity';
import { Graduate } from '../modules/graduate/entities/graduate.entity';
import { JobApplication } from '../modules/job-seeker/entities/job-application.entity';
import { JobSeeker } from '../modules/job-seeker/entities/job-seeker.entity';
import { Job } from '../modules/job/entities/job.entity';
import { Notification } from '../modules/notification/entities/notification.entity';
import { BountyConfiguration } from '../modules/referral/entities/bounty-configuration.entity';
import { ReferralPartner } from '../modules/referral/entities/referral-partner.entity';
import { Referral } from '../modules/referral/entities/referral.entity';
import { UserRoleEntity } from '../modules/roles/entities/user-role.entity';
import { CreditUsageHistory } from '../modules/subscription/entities/credit-usage-history.entity';
import { VideoJD } from '../modules/video-jd/entities/video-jd.entity';
import { VideoResponse } from '../modules/video-response/entities/video-response.entity';
import { Waitlist } from '../modules/waitlist/entities/waitlist.entity';

// Load environment variables
config();

/**
 * Safe Schema Synchronization Script
 *
 * This script uses migrations to safely update the database schema.
 * It will only create missing tables and columns, without dropping anything.
 */
async function synchronizeSchema() {
  console.log('🔄 Starting SAFE database schema synchronization...');
  console.log('📝 This will ONLY create missing tables/columns, no drops allowed.');
  console.log('🛡️  Existing data and types will be preserved.');

  // Get database connection info from environment variables
  const dbHost = process.env.DB_HOST || 'localhost';
  const dbPort = parseInt(process.env.DB_PORT || '5432');
  const dbName = process.env.DB_NAME || '';
  const dbUsername = process.env.DB_USERNAME || '';
  const dbPassword = process.env.DB_PASSWORD || '';

  console.log(`📡 Connecting to database: ${dbHost}:${dbPort}/${dbName}`);

  // Create a DataSource without synchronize
  const AppDataSource = new DataSource({
    type: 'postgres',
    host: dbHost,
    port: dbPort,
    username: dbUsername,
    password: dbPassword,
    database: dbName,
    entities: [
      // Core entities
      Company,
      CompanyMember,
      CompanyInvitation,
      Candidate,
      Job,
      Notification,
      VideoJD,
      VideoResponse,
      JobSeeker,
      JobApplication,
      UserRoleEntity,
      ScoutedCandidate,
      CreditPurchase,

      // Additional entities
      Graduate,
      Approval,
      ApprovalStep,
      Offer,
      CandidateProfile,
      CandidateApplication,
      CandidateEvaluation,
      Feedback,
      CreditUsageHistory,
      Waitlist,
      Contact,
      FeatureFlagEntity,

      // Assessment and Career entities
      RecruitmentAssessment,
      CareerInsight,

      // Comparison entity
      CandidateComparison,

      // Email entity
      EmailHistory,

      // Referral entities
      BountyConfiguration,
      ReferralPartner,
      Referral,
    ],
    synchronize: false, // Never use synchronize in production
    dropSchema: false,
    ssl:
      process.env.DB_SSL === 'require'
        ? {
            rejectUnauthorized: false,
          }
        : false,
  });

  try {
    // Initialize the data source
    console.log('🔌 Initializing database connection...');
    await AppDataSource.initialize();
    console.log('✅ Database connection established.');

    // Get the query runner
    const queryRunner = AppDataSource.createQueryRunner();
    await queryRunner.connect();

    console.log('🔍 Checking for missing tables...');

    // Get metadata for all entities
    const entityMetadatas = AppDataSource.entityMetadatas;

    // Track what we create
    let tablesCreated = 0;
    let columnsAdded = 0;
    let indexesCreated = 0;
    let foreignKeysCreated = 0;

    for (const metadata of entityMetadatas) {
      const tableName = metadata.tableName;

      // Check if table exists
      const tableExists = await queryRunner.hasTable(tableName);

      if (!tableExists) {
        console.log(`📋 Creating table: ${tableName}`);

        // Create table from metadata
        const table = new Table({
          name: tableName,
          columns: metadata.columns.map((column) => ({
            name: column.databaseName,
            type: column.type as string,
            isPrimary: column.isPrimary,
            isNullable: column.isNullable,
            isUnique: false, // Will be handled by unique indices
            isGenerated: column.isGenerated,
            generationStrategy: column.generationStrategy,
            default: column.default,
            comment: column.comment,
            precision: column.precision,
            scale: column.scale,
            length: column.length as string,
            enum: column.enum as string[],
            enumName: column.enumName,
            isArray: column.isArray,
          })),
        });

        await queryRunner.createTable(table, true);
        tablesCreated++;

        // Create indexes for new table
        for (const index of metadata.indices) {
          try {
            const tableIndex = new TableIndex({
              name: index.name,
              columnNames: index.columns.map((col) => col.databaseName),
              isUnique: index.isUnique,
              isSpatial: index.isSpatial,
              isFulltext: index.isFulltext,
              where: index.where,
            });
            await queryRunner.createIndex(tableName, tableIndex);
            indexesCreated++;
          } catch (error: any) {
            if (!error.message.includes('already exists')) {
              console.warn(`⚠️  Warning creating index on ${tableName}:`, error.message);
            }
          }
        }

        // Create foreign keys for new table
        for (const foreignKey of metadata.foreignKeys) {
          try {
            const tableForeignKey = new TableForeignKey({
              name: foreignKey.name,
              columnNames: foreignKey.columnNames,
              referencedColumnNames: foreignKey.referencedColumnNames,
              referencedTableName: foreignKey.referencedTablePath,
              onDelete: foreignKey.onDelete,
              onUpdate: foreignKey.onUpdate,
              deferrable: foreignKey.deferrable,
            });
            await queryRunner.createForeignKey(tableName, tableForeignKey);
            foreignKeysCreated++;
          } catch (error: any) {
            if (!error.message.includes('already exists')) {
              console.warn(`⚠️  Warning creating foreign key on ${tableName}:`, error.message);
            }
          }
        }
      } else {
        // Table exists, check for missing columns
        const tableColumns = await queryRunner.getTable(tableName);
        if (!tableColumns) continue;

        for (const column of metadata.columns) {
          const columnExists = tableColumns.columns.some((c) => c.name === column.databaseName);

          if (!columnExists) {
            console.log(`📝 Adding column: ${tableName}.${column.databaseName}`);
            const tableColumn = new TableColumn({
              name: column.databaseName,
              type: column.type as string,
              isPrimary: column.isPrimary,
              isNullable: column.isNullable,
              isUnique: false, // Will be handled by unique indices
              isGenerated: column.isGenerated,
              generationStrategy: column.generationStrategy,
              default: column.default,
              comment: column.comment,
              precision: column.precision,
              scale: column.scale,
              length: column.length as string,
              enum: column.enum as string[],
              enumName: column.enumName,
              isArray: column.isArray,
            });

            try {
              await queryRunner.addColumn(tableName, tableColumn);
              columnsAdded++;
            } catch (error: any) {
              // Handle enum type issues
              if (error.message.includes('type') && error.message.includes('does not exist')) {
                console.log(`🔧 Creating enum type for ${column.databaseName}`);

                // Extract enum values from column metadata
                if (column.enum && column.enum.length > 0) {
                  const enumName = `${tableName}_${column.databaseName}_enum`;
                  const enumValues = column.enum.map((v: any) => `'${v}'`).join(', ');

                  try {
                    await queryRunner.query(`CREATE TYPE "${enumName}" AS ENUM (${enumValues})`);
                    // Retry adding the column
                    await queryRunner.addColumn(tableName, tableColumn);
                    columnsAdded++;
                  } catch (enumError: any) {
                    console.warn(`⚠️  Warning creating enum type:`, enumError.message);
                  }
                }
              } else if (!error.message.includes('already exists')) {
                console.warn(
                  `⚠️  Warning adding column ${tableName}.${column.databaseName}:`,
                  error.message,
                );
              }
            }
          }
        }

        // Check for missing indexes
        const table = await queryRunner.getTable(tableName);
        const tableIndexes = table?.indices || [];
        for (const index of metadata.indices) {
          const indexExists = tableIndexes.some((i) => i.name === index.name);
          if (!indexExists) {
            try {
              const tableIndex = new TableIndex({
                name: index.name,
                columnNames: index.columns.map((col) => col.databaseName),
                isUnique: index.isUnique,
                isSpatial: index.isSpatial,
                isFulltext: index.isFulltext,
                where: index.where,
              });
              await queryRunner.createIndex(tableName, tableIndex);
              indexesCreated++;
            } catch (error: any) {
              if (!error.message.includes('already exists')) {
                console.warn(`⚠️  Warning creating index:`, error.message);
              }
            }
          }
        }

        // Check for missing foreign keys
        const tableForeignKeys = table?.foreignKeys || [];
        for (const foreignKey of metadata.foreignKeys) {
          const fkExists = tableForeignKeys.some((fk) => fk.name === foreignKey.name);
          if (!fkExists) {
            try {
              const tableForeignKey = new TableForeignKey({
                name: foreignKey.name,
                columnNames: foreignKey.columnNames,
                referencedColumnNames: foreignKey.referencedColumnNames,
                referencedTableName: foreignKey.referencedTablePath,
                onDelete: foreignKey.onDelete,
                onUpdate: foreignKey.onUpdate,
                deferrable: foreignKey.deferrable,
              });
              await queryRunner.createForeignKey(tableName, tableForeignKey);
              foreignKeysCreated++;
            } catch (error: any) {
              if (!error.message.includes('already exists')) {
                console.warn(`⚠️  Warning creating foreign key:`, error.message);
              }
            }
          }
        }
      }
    }

    // Release query runner
    await queryRunner.release();

    console.log('');
    console.log('✅ Safe schema synchronization completed successfully!');
    console.log('📊 Summary:');
    console.log(`   - Tables created: ${tablesCreated}`);
    console.log(`   - Columns added: ${columnsAdded}`);
    console.log(`   - Indexes created: ${indexesCreated}`);
    console.log(`   - Foreign keys created: ${foreignKeysCreated}`);
    console.log('💾 All existing data has been preserved.');

    // Close the connection
    await AppDataSource.destroy();
    console.log('🔌 Database connection closed.');

    console.log('');
    console.log('✨ Safe schema synchronization process completed!');
    console.log('');
    console.log('📝 Next steps:');
    console.log('1. Verify the schema changes are correct');
    console.log('2. Consider running migrations for any complex schema changes:');
    console.log('   pnpm migration:run');
  } catch (error) {
    console.error('❌ Error during safe schema synchronization:', error);
    process.exit(1);
  }
}

// Run the schema synchronization function
synchronizeSchema();
