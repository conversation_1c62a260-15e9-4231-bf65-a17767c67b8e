import { MigrationInterface, QueryRunner } from 'typeorm';
import dataSource from '../config/migration.config';

async function markMigrationsAsRun() {
  try {
    await dataSource.initialize();
    console.log('Data source initialized');

    const queryRunner = dataSource.createQueryRunner();
    await queryRunner.connect();

    // Check if migrations table exists
    const migrationsTableExists = await queryRunner.hasTable('migrations');
    if (!migrationsTableExists) {
      console.log('Creating migrations table...');
      await queryRunner.query(`
        CREATE TABLE IF NOT EXISTS "migrations" (
          "id" SERIAL NOT NULL,
          "timestamp" bigint NOT NULL,
          "name" varchar NOT NULL,
          CONSTRAINT "PK_migrations" PRIMARY KEY ("id")
        )
      `);
    }

    // Mark InitialMigration as run
    const initialMigrationExists = await queryRunner.query(
      `SELECT * FROM migrations WHERE name = 'InitialMigration1754292681638'`,
    );

    if (initialMigrationExists.length === 0) {
      await queryRunner.query(`INSERT INTO migrations (timestamp, name) VALUES ($1, $2)`, [
        1754292681638,
        'InitialMigration1754292681638',
      ]);
      console.log('Marked InitialMigration as run');
    }

    // Mark AddDualRoleSupport as run since we manually added the columns
    const dualRoleMigrationExists = await queryRunner.query(
      `SELECT * FROM migrations WHERE name = 'AddDualRoleSupport1754350000000'`,
    );

    if (dualRoleMigrationExists.length === 0) {
      await queryRunner.query(`INSERT INTO migrations (timestamp, name) VALUES ($1, $2)`, [
        1754350000000,
        'AddDualRoleSupport1754350000000',
      ]);
      console.log('Marked AddDualRoleSupport as run');
    }

    await queryRunner.release();
    await dataSource.destroy();
    console.log('Done!');
  } catch (error) {
    console.error('Error:', error);
    process.exit(1);
  }
}

markMigrationsAsRun();
