import {
  Call<PERSON><PERSON><PERSON>,
  ExecutionContext,
  Injectable,
  NestInterceptor,
  BadRequestException,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { FastifyRequest } from 'fastify';

interface FieldConfig {
  name: string;
  maxCount?: number;
}

@Injectable()
export class FastifyFileFieldsInterceptor implements NestInterceptor {
  constructor(private readonly fields: FieldConfig[]) {}

  async intercept(context: ExecutionContext, next: CallHandler): Promise<Observable<any>> {
    const request = context.switchToHttp().getRequest<FastifyRequest>();

    try {
      const files: Record<string, Express.Multer.File[]> = {};

      // Check if the request is multipart
      if (!request.isMultipart()) {
        throw new BadRequestException('Request is not multipart');
      }

      // Process each part of the multipart request
      const parts = request.parts();

      for await (const part of parts) {
        if (part.type === 'file') {
          // Find the field configuration
          const fieldConfig = this.fields.find((f) => f.name === part.fieldname);

          if (fieldConfig) {
            // Initialize array if not exists
            if (!files[fieldConfig.name]) {
              files[fieldConfig.name] = [];
            }

            // Check max count
            if (fieldConfig.maxCount && files[fieldConfig.name].length >= fieldConfig.maxCount) {
              throw new BadRequestException(
                `Too many files for field ${fieldConfig.name}. Maximum is ${fieldConfig.maxCount}`,
              );
            }

            // Convert to Express.Multer.File format for compatibility
            const fileBuffer = await part.toBuffer();
            const file: Express.Multer.File = {
              fieldname: part.fieldname,
              originalname: part.filename,
              encoding: part.encoding,
              mimetype: part.mimetype,
              buffer: fileBuffer,
              size: fileBuffer.length,
              stream: null as any,
              destination: '',
              filename: part.filename,
              path: '',
            };

            files[fieldConfig.name].push(file);
          }
        } else if (part.type === 'field') {
          // Handle regular fields
          (request as any).body = (request as any).body || {};
          (request as any).body[part.fieldname] = part.value;
        }
      }

      // Attach files to request
      (request as any).files = files;

      return next.handle();
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException('Failed to process file upload');
    }
  }
}
