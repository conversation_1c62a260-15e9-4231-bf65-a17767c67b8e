import {
  <PERSON><PERSON><PERSON><PERSON>,
  ExecutionContext,
  Injectable,
  NestInterceptor,
  BadRequestException,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { FastifyRequest } from 'fastify';

@Injectable()
export class FastifyFileInterceptor implements NestInterceptor {
  constructor(private readonly fieldName: string) {}

  async intercept(context: ExecutionContext, next: CallHandler): Promise<Observable<any>> {
    const request = context.switchToHttp().getRequest<FastifyRequest>();

    try {
      // Parse multipart data
      const data = await request.file();

      if (!data) {
        throw new BadRequestException('No file uploaded');
      }

      // Convert to Express.Multer.File format for compatibility
      const file: Express.Multer.File = {
        fieldname: data.fieldname,
        originalname: data.filename,
        encoding: data.encoding,
        mimetype: data.mimetype,
        buffer: await data.toBuffer(),
        size: 0, // Will be calculated below
        stream: null as any,
        destination: '',
        filename: data.filename,
        path: '',
      };

      // Calculate size from buffer
      file.size = file.buffer.length;

      // Attach file to request
      (request as any).file = file;

      return next.handle();
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException('Failed to process file upload');
    }
  }
}
